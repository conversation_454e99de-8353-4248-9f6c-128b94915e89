<template>
  <div class="data-table-container">
    <!-- Header Section -->
    <div class="mb-6 space-y-4">
      <!-- Title and Actions -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h3 v-if="title" class="text-lg font-semibold text-gray-900">
            {{ title }}
          </h3>
          <p v-if="description" class="text-sm text-gray-600 mt-1">
            {{ description }}
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center gap-2">
          <slot name="actions" :selected="selectedRows" :hasSelection="hasSelection">
            <button v-if="config.exportable" @click="exportToCSV()"
              class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-4 focus:ring-gray-100">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path
                  d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" />
              </svg>
              Export
            </button>
          </slot>
        </div>
      </div>

      <!-- Search and Filters -->
      <DataTableFilters v-if="config.showSearch || config.filterable" :global-filter="globalFilter"
        :column-filters="columnFilters" :columns="columns" :config="config" @update:global-filter="setGlobalFilter"
        @update:column-filter="setColumnFilter" @clear-filters="clearFilters" />
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="animate-pulse">
      <div class="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="h-4 bg-gray-200 rounded w-1/4"></div>
        </div>
        <div class="divide-y divide-gray-200">
          <div v-for="i in 5" :key="i" class="px-6 py-4">
            <div class="flex space-x-4">
              <div class="h-4 bg-gray-200 rounded w-1/4"></div>
              <div class="h-4 bg-gray-200 rounded w-1/3"></div>
              <div class="h-4 bg-gray-200 rounded w-1/6"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Table -->
    <div v-else class="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200">
      <!-- Desktop Table -->
      <div class="hidden md:block overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <!-- Table Header -->
          <DataTableHeader :columns="columns" :config="config" :sort-by="sortBy" :sort-direction="sortDirection"
            :is-all-selected="isAllSelected" @sort="sort" @toggle-select-all="toggleSelectAll" />

          <!-- Table Body -->
          <tbody class="bg-white divide-y divide-gray-200">
            <DataTableRow v-for="(row, index) in paginatedData" :key="row.id || index" :row="row"
              :index="(currentPage - 1) * config.itemsPerPage + index" :columns="columns" :config="config"
              :is-selected="isRowSelected(row, index)" @toggle-selection="toggleRowSelection"
              @row-click="$emit('row-click', row, index)">
              <template v-for="(_, slot) in $slots" #[slot]="slotProps">
                <slot :name="slot" v-bind="slotProps" />
              </template>
            </DataTableRow>

            <!-- Empty State -->
            <tr v-if="paginatedData.length === 0">
              <td :colspan="columns.length + (config.selectable ? 1 : 0)" class="px-6 py-12 text-center">
                <div class="flex flex-col items-center justify-center">
                  <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <h3 class="text-lg font-medium text-gray-900 mb-2">No data found</h3>
                  <p class="text-gray-500">
                    {{ globalFilter ? 'No results match your search criteria.' : 'No data available to display.' }}
                  </p>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Mobile Cards View -->
      <div class="md:hidden">
        <div v-if="paginatedData.length === 0" class="p-6 text-center">
          <div class="flex flex-col items-center justify-center">
            <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No data found</h3>
            <p class="text-gray-500">
              {{ globalFilter ? 'No results match your search criteria.' : 'No data available to display.' }}
            </p>
          </div>
        </div>

        <div v-else class="divide-y divide-gray-200">
          <div v-for="(row, index) in paginatedData" :key="row.id || index" :class="[
            'p-4 hover:bg-gray-50 transition-colors duration-150',
            isRowSelected(row, index) ? 'bg-blue-50' : '',
            config.clickableRows ? 'cursor-pointer' : ''
          ]" @click="$emit('row-click', row, index)">
            <!-- Selection checkbox for mobile -->
            <div v-if="config.selectable" class="flex items-center mb-3">
              <input type="checkbox" :checked="isRowSelected(row, index)" @click.stop
                @change="toggleRowSelection(row, index)"
                class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
              <span class="ml-2 text-sm text-gray-600">Select item</span>
            </div>

            <!-- Mobile card content -->
            <div class="space-y-3">
              <div v-for="column in columns.filter(col => col.key !== 'actions' && !col.hideOnMobile)" :key="column.key"
                class="flex justify-between items-start">
                <div class="text-sm font-medium text-gray-500 uppercase tracking-wide min-w-0 flex-shrink-0 w-1/3">
                  {{ column.label || column.key }}
                </div>
                <div class="text-sm text-gray-900 text-right min-w-0 flex-1 ml-4">
                  <!-- Use the same cell rendering logic as desktop -->
                  <slot v-if="$slots[`cell-${column.key}`]" :name="`cell-${column.key}`" :row="row" :column="column"
                    :value="getCellValue(row, column.key)" :index="index" />
                  <div v-else-if="column.type === 'badge'" class="inline-flex justify-end">
                    <span :class="[
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      getBadgeClass(getCellValue(row, column.key), column.badgeColors)
                    ]">
                      {{ formatCellValue(getCellValue(row, column.key), column, row) }}
                    </span>
                  </div>
                  <div v-else-if="column.type === 'date'">
                    {{ formatDate(getCellValue(row, column.key), column.dateFormat) }}
                  </div>
                  <div v-else-if="column.type === 'currency'" class="font-medium">
                    {{ formatCurrency(getCellValue(row, column.key), column.currency) }}
                  </div>
                  <div v-else-if="column.type === 'boolean'" class="flex items-center justify-end">
                    <div :class="[
                      'flex-shrink-0 w-2 h-2 rounded-full mr-2',
                      getCellValue(row, column.key) ? 'bg-green-400' : 'bg-red-400'
                    ]"></div>
                    <span class="text-sm">
                      {{ getCellValue(row, column.key) ? (column.trueText || 'Yes') : (column.falseText || 'No') }}
                    </span>
                  </div>
                  <div v-else>
                    {{ formatCellValue(getCellValue(row, column.key), column, row) }}
                  </div>
                </div>
              </div>

              <!-- Actions for mobile -->
              <div v-if="columns.some(col => col.key === 'actions' || col.type === 'actions')"
                class="pt-3 border-t border-gray-200">
                <div class="flex justify-end space-x-2">
                  <slot name="cell-actions" :row="row" :index="index">
                    <button @click.stop="$emit('edit', row, index)"
                      class="text-primary hover:text-primary-hbr text-sm font-medium">
                      Edit
                    </button>
                    <button @click.stop="$emit('delete', row, index)"
                      class="text-red-600 hover:text-red-800 text-sm font-medium">
                      Delete
                    </button>
                  </slot>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <DataTablePagination v-if="config.showPagination && (config.serverSide || totalPages > 1)"
        :current-page="currentPage" :total-pages="totalPages" :total-items="totalItems"
        :items-per-page="config.itemsPerPage" :items-per-page-options="itemsPerPageOptions"
        @update:current-page="goToPage" @update:items-per-page="setItemsPerPage" @prev-page="prevPage"
        @next-page="nextPage" />
    </div>

    <!-- Selection Info -->
    <div v-if="hasSelection" class="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
      <div class="flex items-center justify-between">
        <span class="text-sm text-blue-700">
          {{ selectedRows.length }} {{ selectedRows.length === 1 ? 'item' : 'items' }} selected
        </span>
        <button @click="resetSelection" class="text-sm text-blue-600 hover:text-blue-800">
          Clear selection
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  computed,
  watch,
} from 'vue'

import useDataTable from '../../composables/useDataTable.js'
import DataTableFilters from './DataTableFilters.vue'
import DataTableHeader from './DataTableHeader.vue'
import DataTablePagination from './DataTablePagination.vue'
import DataTableRow from './DataTableRow.vue'

// Props
const props = defineProps({
  data: {
    type: [Array, Object],
    default: () => []
  },
  columns: {
    type: Array,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  config: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'row-click',
  'selection-change',
  'data-change',
  'server-search',
  'server-filter',
  'server-sort',
  'server-paginate'
])

// Use the data table composable
const {
  // State
  config,
  currentPage,
  itemsPerPageOptions,
  sortBy,
  sortDirection,
  globalFilter,
  columnFilters,
  selectedRows,

  // Computed
  paginatedData,
  totalPages,
  totalItems,
  hasSelection,
  isAllSelected,

  // Methods
  setData,
  sort,
  setGlobalFilter,
  setColumnFilter,
  clearFilters,
  goToPage,
  nextPage,
  prevPage,
  setItemsPerPage,
  toggleRowSelection,
  toggleSelectAll,
  resetSelection,
  isRowSelected,
  exportToCSV
} = useDataTable(props.data, props.config, emit)

// Watch for data changes
watch(() => props.data, (newData) => {
  setData(newData)
}, { immediate: true })

// Watch for selection changes
watch(selectedRows, (newSelection) => {
  emit('selection-change', newSelection)
}, { deep: true })



// Helper methods for mobile view
const getCellValue = (row, key) => {
  return key.split('.').reduce((obj, k) => obj?.[k], row)
}

const formatCellValue = (value, column, row = null) => {
  if (value === null || value === undefined) return column.emptyText || '-'

  if (column.formatter && typeof column.formatter === 'function') {
    return column.formatter(value, row)
  }

  return String(value)
}

const formatDate = (value, format = 'short') => {
  if (!value) return '-'

  const date = new Date(value)
  if (isNaN(date.getTime())) return value

  const options = {
    short: { year: 'numeric', month: 'short', day: 'numeric' },
    long: { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' },
    time: { hour: '2-digit', minute: '2-digit' }
  }

  return date.toLocaleDateString('en-US', options[format] || options.short)
}

const formatCurrency = (value, currency = 'USD') => {
  if (value === null || value === undefined) return '-'

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(value)
}

const getBadgeClass = (value, colors = {}) => {
  const defaultColors = {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-red-100 text-red-800',
    pending: 'bg-yellow-100 text-yellow-800',
    1: 'bg-green-100 text-green-800',      // Active (numeric)
    0: 'bg-red-100 text-red-800',         // Inactive (numeric)
    '1': 'bg-green-100 text-green-800',   // Active (string)
    '0': 'bg-red-100 text-red-800',       // Inactive (string)
    default: 'bg-gray-100 text-gray-800'
  }

  const colorMap = { ...defaultColors, ...colors }

  // Try exact value match first (for numeric values)
  if (colorMap[value] !== undefined) {
    return colorMap[value]
  }

  // Then try string/lowercase match
  const key = String(value).toLowerCase()
  return colorMap[key] || colorMap.default
}
</script>

<style scoped>
.data-table-container {
  @apply w-full;
}
</style>
