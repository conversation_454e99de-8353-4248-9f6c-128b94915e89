<template>
  <thead class="bg-gray-50 sticky top-0 z-10">
    <tr>
      <!-- Selection Column -->
      <th v-if="config.selectable" scope="col" class="relative w-12 px-6 sm:w-16 sm:px-8">
        <input v-if="config.multiSelect" type="checkbox" :checked="isAllSelected" @change="$emit('toggle-select-all')"
          class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
      </th>

      <!-- Column Headers -->
      <th v-for="column in columns" :key="column.key" scope="col" :class="[
        'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
        column.sortable !== false && config.sortable ? 'cursor-pointer hover:bg-gray-100 select-none' : '',
        column.class || ''
      ]" @click="handleSort(column)">
        <div class="flex items-center space-x-1">
          <span>{{ column.label || column.key }}</span>

          <!-- Sort Icons -->
          <div v-if="column.sortable !== false && config.sortable" class="flex flex-col">
            <svg :class="[
              'w-3 h-3 transition-colors',
              sortBy === column.key && sortDirection === 'asc'
                ? 'text-primary'
                : 'text-gray-400'
            ]" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" />
            </svg>
            <svg :class="[
              'w-3 h-3 -mt-1 transition-colors',
              sortBy === column.key && sortDirection === 'desc'
                ? 'text-primary'
                : 'text-gray-400'
            ]" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
            </svg>
          </div>

          <!-- Column Filter Indicator -->
          <div v-if="column.filterable !== false && config.filterable && hasColumnFilter(column.key)"
            class="w-2 h-2 bg-primary rounded-full" :title="`Filtered by: ${getColumnFilter(column.key)}`"></div>
        </div>

        <!-- Column Description/Tooltip -->
        <div v-if="column.description" class="mt-1 text-xs text-gray-400 normal-case font-normal">
          {{ column.description }}
        </div>
      </th>


    </tr>
  </thead>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  columns: {
    type: Array,
    required: true
  },
  config: {
    type: Object,
    required: true
  },
  sortBy: {
    type: String,
    default: ''
  },
  sortDirection: {
    type: String,
    default: 'asc'
  },
  isAllSelected: {
    type: Boolean,
    default: false
  },
  columnFilters: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['sort', 'toggle-select-all'])

// Computed

// Methods
const handleSort = (column) => {
  if (column.sortable !== false && props.config.sortable) {
    emit('sort', column.key)
  }
}

const hasColumnFilter = (columnKey) => {
  return props.columnFilters && props.columnFilters[columnKey]
}

const getColumnFilter = (columnKey) => {
  return props.columnFilters ? props.columnFilters[columnKey] : ''
}
</script>

<style scoped>
/* Custom styles for sort indicators */
.sort-indicator {
  transition: all 0.2s ease-in-out;
}
</style>
