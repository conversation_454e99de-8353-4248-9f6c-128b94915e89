import { ref } from 'vue'

export function usePrint() {
  const showPrintModal = ref(false)
  const printOptions = ref({
    printAll: true,
    printSelected: false,
    includeFilters: true,
    orientation: 'portrait', // 'portrait' or 'landscape'
    paperSize: 'A4', // 'A4', 'Letter', 'Legal'
    fontSize: 'medium' // 'small', 'medium', 'large'
  })

  // Dynamic print function
  const printTable = (config) => {
    const {
      data = [],
      selectedData = [],
      columns = [],
      title = 'Data Report',
      subtitle = '',
      filters = {},
      options = {}
    } = config

    // Merge with default options
    const finalOptions = { ...printOptions.value, ...options }
    
    // Determine what data to print
    const dataToPrint = finalOptions.printSelected && selectedData.length > 0 
      ? selectedData 
      : data

    // Generate dynamic subtitle
    const dynamicSubtitle = generateSubtitle(dataToPrint, data, selectedData, filters, finalOptions)

    // Generate print HTML
    const printHTML = generateDynamicPrintHTML({
      data: dataToPrint,
      columns,
      title,
      subtitle: subtitle || dynamicSubtitle,
      options: finalOptions
    })

    // Open print window
    openPrintWindow(printHTML)
  }

  // Generate dynamic subtitle based on data
  const generateSubtitle = (dataToPrint, allData, selectedData, filters, options) => {
    let subtitle = ''
    
    // Data count info
    if (options.printSelected && selectedData.length > 0) {
      subtitle += `Selected Items: ${dataToPrint.length} of ${allData.length}`
    } else {
      subtitle += `Total Items: ${dataToPrint.length}`
    }

    // Filter info
    if (options.includeFilters && Object.keys(filters).length > 0) {
      const filterText = Object.entries(filters)
        .filter(([key, value]) => value && value !== '')
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ')
      
      if (filterText) {
        subtitle += ` | Filters: ${filterText}`
      }
    }

    return subtitle
  }

  // Generate dynamic HTML based on columns configuration
  const generateDynamicPrintHTML = ({ data, columns, title, subtitle, options }) => {
    const styles = generatePrintStyles(options)
    const tableHTML = generateTableHTML(data, columns)

    return `
      <!DOCTYPE html>
      <html>
      <head>
          <title>${title}</title>
          <style>${styles}</style>
      </head>
      <body class="${options.orientation} ${options.paperSize} ${options.fontSize}">
          <div class="print-container">
              <div class="header">
                  <h1>${title}</h1>
                  ${subtitle ? `<div class="subtitle">${subtitle}</div>` : ''}
              </div>
              
              ${tableHTML}
              
              <div class="footer">
                  <div class="print-info">
                      Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
                  </div>
              </div>
          </div>
      </body>
      </html>
    `
  }

  // Generate dynamic table HTML based on columns
  const generateTableHTML = (data, columns) => {
    if (!data.length) return '<p>No data to display</p>'

    // Filter visible columns (exclude actions, etc.)
    const printableColumns = columns.filter(col => 
      col.key !== 'actions' && 
      !col.hideOnPrint && 
      col.key !== 'selection'
    )

    const headers = printableColumns.map(col => col.label || col.key).join('')
    
    const rows = data.map(row => {
      const cells = printableColumns.map(col => {
        let value = getCellValue(row, col)
        value = formatCellValue(value, col)
        return `<td>${value}</td>`
      }).join('')
      return `<tr>${cells}</tr>`
    }).join('')

    return `
      <table class="print-table">
          <thead>
              <tr>${headers}</tr>
          </thead>
          <tbody>
              ${rows}
          </tbody>
      </table>
    `
  }

  // Get cell value with nested property support
  const getCellValue = (row, column) => {
    const keys = column.key.split('.')
    let value = row
    
    for (const key of keys) {
      value = value?.[key]
      if (value === undefined || value === null) break
    }
    
    return value
  }

  // Format cell value based on column type
  const formatCellValue = (value, column) => {
    if (value === null || value === undefined) {
      return column.emptyText || '-'
    }

    // Apply formatter if exists
    if (column.formatter && typeof column.formatter === 'function') {
      return column.formatter(value)
    }

    // Handle different column types
    switch (column.type) {
      case 'date':
        return formatDate(value, column.dateFormat)
      case 'currency':
        return formatCurrency(value, column.currency)
      case 'badge':
        return value // Just return text for print
      case 'boolean':
        return value ? 'Yes' : 'No'
      case 'array':
        return Array.isArray(value) ? value.join(', ') : value
      default:
        return String(value)
    }
  }

  // Format date helper
  const formatDate = (value, format = 'short') => {
    if (!value) return ''
    const date = new Date(value)
    if (isNaN(date.getTime())) return value

    switch (format) {
      case 'short':
        return date.toLocaleDateString()
      case 'long':
        return date.toLocaleDateString('en-US', { 
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        })
      case 'datetime':
        return date.toLocaleString()
      default:
        return date.toLocaleDateString()
    }
  }

  // Format currency helper
  const formatCurrency = (value, currency = 'USD') => {
    if (!value && value !== 0) return ''
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(value)
  }

  // Generate print styles based on options
  const generatePrintStyles = (options) => {
    const fontSizes = {
      small: { base: '12px', header: '18px', subtitle: '14px' },
      medium: { base: '14px', header: '22px', subtitle: '16px' },
      large: { base: '16px', header: '26px', subtitle: '18px' }
    }

    const size = fontSizes[options.fontSize] || fontSizes.medium

    return `
      * { box-sizing: border-box; }
      
      body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          font-size: ${size.base};
          line-height: 1.4;
      }
      
      .print-container {
          max-width: 100%;
          margin: 0 auto;
      }
      
      .header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #333;
          padding-bottom: 20px;
      }
      
      h1 {
          color: #333;
          margin: 0 0 10px 0;
          font-size: ${size.header};
          font-weight: bold;
      }
      
      .subtitle {
          color: #666;
          font-size: ${size.subtitle};
          margin: 0;
      }
      
      .print-table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
      }
      
      .print-table th,
      .print-table td {
          border: 1px solid #ddd;
          padding: 8px 12px;
          text-align: left;
          vertical-align: top;
      }
      
      .print-table th {
          background-color: #f8f9fa;
          font-weight: bold;
          color: #333;
      }
      
      .print-table tr:nth-child(even) {
          background-color: #f8f9fa;
      }
      
      .footer {
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #ddd;
          text-align: center;
      }
      
      .print-info {
          color: #666;
          font-size: 12px;
      }
      
      /* Orientation styles */
      .landscape {
          /* Landscape specific styles */
      }
      
      .portrait {
          /* Portrait specific styles */
      }
      
      /* Paper size styles */
      .A4 { /* A4 specific styles */ }
      .Letter { /* Letter specific styles */ }
      .Legal { /* Legal specific styles */ }
      
      @media print {
          body { margin: 0; padding: 15px; }
          .no-print { display: none !important; }
          .print-table { page-break-inside: avoid; }
          .print-table thead { display: table-header-group; }
          .print-table tbody { display: table-row-group; }
      }
      
      @page {
          margin: 1in;
          size: ${options.paperSize} ${options.orientation};
      }
    `
  }

  // Open print window
  const openPrintWindow = (htmlContent) => {
    const printWindow = window.open('', '_blank', 'width=800,height=600')
    printWindow.document.open()
    printWindow.document.write(htmlContent)
    printWindow.document.close()
    printWindow.focus()
    
    // Wait for content to load then print
    printWindow.onload = () => {
      printWindow.print()
      printWindow.close()
    }
  }

  // Show print options modal
  const showPrintOptions = () => {
    showPrintModal.value = true
  }

  const hidePrintOptions = () => {
    showPrintModal.value = false
  }

  return {
    // State
    showPrintModal,
    printOptions,
    
    // Methods
    printTable,
    showPrintOptions,
    hidePrintOptions
  }
}
