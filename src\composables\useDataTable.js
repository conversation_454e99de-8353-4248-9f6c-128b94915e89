import {
  computed,
  reactive,
  ref,
  watch,
} from 'vue'

export default function useDataTable(initialData = [], initialConfig = {}) {
  // Reactive state
  const data = ref([...initialData])
  const loading = ref(false)
  const error = ref(null)

  // Server-side pagination state
  const serverPagination = ref(null)
  const isServerSide = ref(false)

  // Configuration with defaults
  const config = reactive({
    itemsPerPage: 10,
    sortable: true,
    filterable: true,
    selectable: false,
    multiSelect: false,
    showPagination: true,
    showSearch: true,
    exportable: false,
    serverSide: false, // Enable server-side pagination
    ...initialConfig
  })

  // Pagination state
  const currentPage = ref(1)
  const itemsPerPageOptions = ref([5, 10, 25, 50, 100])

  // Sorting state
  const sortBy = ref('')
  const sortDirection = ref('asc') // 'asc' | 'desc'

  // Filtering state
  const globalFilter = ref('')
  const columnFilters = ref({})

  // Selection state
  const selectedRows = ref([])
  const selectAll = ref(false)

  // Computed properties
  const filteredData = computed(() => {
    let filtered = [...data.value]

    // Apply global filter
    if (globalFilter.value) {
      const searchTerm = globalFilter.value.toLowerCase()
      filtered = filtered.filter(item =>
        Object.values(item).some(value =>
          String(value).toLowerCase().includes(searchTerm)
        )
      )
    }

    // Apply column filters
    Object.entries(columnFilters.value).forEach(([column, filterValue]) => {
      if (filterValue) {
        filtered = filtered.filter(item =>
          String(item[column]).toLowerCase().includes(filterValue.toLowerCase())
        )
      }
    })

    return filtered
  })

  const sortedData = computed(() => {
    if (!sortBy.value) return filteredData.value

    return [...filteredData.value].sort((a, b) => {
      const aVal = a[sortBy.value]
      const bVal = b[sortBy.value]

      let comparison = 0
      if (aVal > bVal) comparison = 1
      if (aVal < bVal) comparison = -1

      return sortDirection.value === 'desc' ? -comparison : comparison
    })
  })

  const paginatedData = computed(() => {
    // For server-side pagination, return data as-is
    if (config.serverSide || isServerSide.value) {
      return data.value
    }

    if (!config.showPagination) return sortedData.value

    const start = (currentPage.value - 1) * config.itemsPerPage
    const end = start + config.itemsPerPage
    return sortedData.value.slice(start, end)
  })

  const totalPages = computed(() => {
    if (config.serverSide || isServerSide.value) {
      return serverPagination.value?.last_page || 1
    }
    return Math.ceil(filteredData.value.length / config.itemsPerPage)
  })

  const totalItems = computed(() => {
    if (config.serverSide || isServerSide.value) {
      return serverPagination.value?.total || data.value.length
    }
    return filteredData.value.length
  })

  const hasSelection = computed(() => selectedRows.value.length > 0)

  const isAllSelected = computed(() => {
    return paginatedData.value.length > 0 &&
      selectedRows.value.length === paginatedData.value.length
  })

  // Methods
  const setData = (newData) => {
    // Check if data is in Laravel pagination format
    if (newData && typeof newData === 'object' && newData.data && Array.isArray(newData.data)) {
      // Server-side pagination data
      data.value = [...newData.data]
      serverPagination.value = {
        current_page: newData.current_page,
        last_page: newData.last_page,
        per_page: newData.per_page,
        total: newData.total,
        from: newData.from,
        to: newData.to,
        first_page_url: newData.first_page_url,
        last_page_url: newData.last_page_url,
        next_page_url: newData.next_page_url,
        prev_page_url: newData.prev_page_url,
        path: newData.path,
        links: newData.links
      }
      isServerSide.value = true
      currentPage.value = newData.current_page
      config.itemsPerPage = newData.per_page
    } else if (Array.isArray(newData)) {
      // Client-side data
      data.value = [...newData]
      isServerSide.value = false
      serverPagination.value = null
      currentPage.value = 1
    } else {
      // Fallback
      data.value = []
      isServerSide.value = false
      serverPagination.value = null
    }
    resetSelection()
  }

  const setServerData = (paginatedResponse) => {
    setData(paginatedResponse)
  }

  const addRow = (row) => {
    data.value.push(row)
  }

  const updateRow = (index, updatedRow) => {
    if (index >= 0 && index < data.value.length) {
      data.value[index] = { ...data.value[index], ...updatedRow }
    }
  }

  const deleteRow = (index) => {
    if (index >= 0 && index < data.value.length) {
      data.value.splice(index, 1)
      resetSelection()
    }
  }

  const deleteRows = (indices) => {
    const sortedIndices = [...indices].sort((a, b) => b - a)
    sortedIndices.forEach(index => deleteRow(index))
  }

  const sort = (column) => {
    if (sortBy.value === column) {
      sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
    } else {
      sortBy.value = column
      sortDirection.value = 'asc'
    }
  }

  const setGlobalFilter = (value) => {
    globalFilter.value = value
    currentPage.value = 1
  }

  const setColumnFilter = (column, value) => {
    columnFilters.value[column] = value
    currentPage.value = 1
  }

  const clearFilters = () => {
    globalFilter.value = ''
    columnFilters.value = {}
    currentPage.value = 1
  }

  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
    }
  }

  const nextPage = () => {
    if (currentPage.value < totalPages.value) {
      currentPage.value++
    }
  }

  const prevPage = () => {
    if (currentPage.value > 1) {
      currentPage.value--
    }
  }

  const setItemsPerPage = (items) => {
    config.itemsPerPage = items
    currentPage.value = 1
  }

  const toggleRowSelection = (row, index) => {
    if (!config.selectable) return

    const rowId = row.id || index
    const selectedIndex = selectedRows.value.findIndex(selected =>
      (selected.id || selected.index) === rowId
    )

    if (selectedIndex > -1) {
      selectedRows.value.splice(selectedIndex, 1)
    } else {
      if (config.multiSelect) {
        selectedRows.value.push({ ...row, index })
      } else {
        selectedRows.value = [{ ...row, index }]
      }
    }
  }

  const toggleSelectAll = () => {
    if (isAllSelected.value) {
      selectedRows.value = []
    } else {
      selectedRows.value = paginatedData.value.map((row, index) => ({
        ...row,
        index: (currentPage.value - 1) * config.itemsPerPage + index
      }))
    }
  }

  const resetSelection = () => {
    selectedRows.value = []
    selectAll.value = false
  }

  const isRowSelected = (row, index) => {
    const rowId = row.id || index
    return selectedRows.value.some(selected =>
      (selected.id || selected.index) === rowId
    )
  }

  const exportToCSV = (filename = 'data.csv') => {
    if (!config.exportable) return

    const headers = Object.keys(data.value[0] || {})
    const csvContent = [
      headers.join(','),
      ...filteredData.value.map(row =>
        headers.map(header => `"${row[header] || ''}"`).join(',')
      )
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.click()
    window.URL.revokeObjectURL(url)
  }

  // Watch for data changes to reset pagination
  watch(() => data.value.length, () => {
    if (currentPage.value > totalPages.value) {
      currentPage.value = Math.max(1, totalPages.value)
    }
  })

  return {
    // State
    data,
    loading,
    error,
    config,
    currentPage,
    itemsPerPageOptions,
    sortBy,
    sortDirection,
    globalFilter,
    columnFilters,
    selectedRows,
    selectAll,
    serverPagination,
    isServerSide,

    // Computed
    filteredData,
    sortedData,
    paginatedData,
    totalPages,
    totalItems,
    hasSelection,
    isAllSelected,

    // Methods
    setData,
    setServerData,
    addRow,
    updateRow,
    deleteRow,
    deleteRows,
    sort,
    setGlobalFilter,
    setColumnFilter,
    clearFilters,
    goToPage,
    nextPage,
    prevPage,
    setItemsPerPage,
    toggleRowSelection,
    toggleSelectAll,
    resetSelection,
    isRowSelected,
    exportToCSV
  }
}
