import 'vue3-toastify/dist/index.css'

import { defineStore } from 'pinia'
import { toast } from 'vue3-toastify'

import api from '@/api/api'

import useAuthStore from '../auth'

const useUserStore = defineStore("useUserStore", {
    state: () => ({
        users: [],
        user: [],
        totalPages: 1, // Default total pages
        loading: false,
        error: null,
        pagination: null,
        filters: {
            search: '',
            position: '',
            status: '',
            dateRange: null
        },
        sorting: {
            column: '',
            direction: 'asc'
        },
        selectedUsers: []
    }),

    getters: {
        // Get users in DataTable format
        getUsersForTable: (state) => {
            if (state.pagination) {
                // Return server-side pagination format
                return state.pagination
            }
            // Return client-side format
            return state.users
        },

        // Get filtered users count
        filteredUsersCount: (state) => {
            if (state.pagination) {
                return state.pagination.total
            }
            return state.users.length
        },

        // Check if any users are selected
        hasSelectedUsers: (state) => state.selectedUsers.length > 0,

        // Get selected users count
        selectedUsersCount: (state) => state.selectedUsers.length,

        // Get available positions for filter
        availablePositions: (state) => {
            const positions = [...new Set(state.users.map(user => user.user_position).filter(Boolean))]
            return positions.map(pos => ({ value: pos, label: pos }))
        },

        // Get available statuses for filter
        availableStatuses: () => [
            { value: 'active', label: 'Active' },
            { value: 'inactive', label: 'Inactive' },
            { value: 'pending', label: 'Pending' }
        ]
    },

    actions: {
        // Enhanced fetchUser method for DataTable
        async fetchUsers(params = {}) {
            this.loading = true
            this.error = null

            try {
                const queryParams = new URLSearchParams({
                    page: params.page || 1,
                    limit: params.limit || 10,
                    search: params.search || this.filters.search || '',
                    position: params.position || this.filters.position || '',
                    status: params.status || this.filters.status || '',
                    sort_by: params.sortBy || this.sorting.column || '',
                    sort_direction: params.sortDirection || this.sorting.direction || 'asc'
                })

                const response = await api.get(`/user-list?${queryParams}`)

                if (response.data.success) {
                    // Store pagination data for server-side pagination
                    this.pagination = response.data.data
                    this.users = response.data.data.data || []
                    this.totalPages = response.data.data.last_page || 1

                    // Update filters and sorting
                    if (params.search !== undefined) this.filters.search = params.search
                    if (params.position !== undefined) this.filters.position = params.position
                    if (params.status !== undefined) this.filters.status = params.status
                    if (params.sortBy !== undefined) this.sorting.column = params.sortBy
                    if (params.sortDirection !== undefined) this.sorting.direction = params.sortDirection

                    return response.data.data
                } else {
                    throw new Error(response.data.message || 'Failed to fetch users')
                }
            } catch (err) {
                console.error("Error fetching users:", err)
                this.error = err.message || 'Failed to fetch users'
                toast.error(this.error)
                throw err
            } finally {
                this.loading = false
            }
        },

        // Legacy method for backward compatibility
        async fetchUser(page = 1, limit = 20) {
            return await this.fetchUsers({ page, limit })
        },

        // Set filters
        setFilters(filters) {
            this.filters = { ...this.filters, ...filters }
        },

        // Set sorting
        setSorting(column, direction) {
            this.sorting = { column, direction }
        },

        // Set selected users
        setSelectedUsers(users) {
            this.selectedUsers = users
        },

        // Clear selection
        clearSelection() {
            this.selectedUsers = []
        },

        async createUser(userData) {
            await api.post("/createUser", userData).then((response) => {
                if (response.data) {
                    this.user.push(response.data.data.user);
                    toast.success(response.data.message);
                    return true;
                }
            }).catch((err) => {
                console.log(err);
                // console.log(err.response.data.message);
                toast.error(err.response.data.message);
            });
        },

        async updateUser(userData) {
            await api.post("/editUser", userData).then((response) => {
                if (response.data) {
                    toast.success(response.data.message);
                }
            }).catch((err) => {
                console.log(err);
                toast.error(err.response.data.message);
            });
        },
        // Enhanced delete user method
        async deleteUser(id) {
            try {
                const response = await api.delete(`/deleteUser/${id}`)

                if (response.data.success) {
                    // Remove user from local state
                    this.users = this.users.filter((user) => user.id !== id)
                    this.user = this.user.filter((user) => user.id !== id)

                    // Remove from selected users if present
                    this.selectedUsers = this.selectedUsers.filter(user => user.id !== id)

                    toast.success(response.data.message)

                    // Refresh data instead of page reload
                    await this.fetchUsers()

                    return true
                } else {
                    throw new Error(response.data.message || 'Failed to delete user')
                }
            } catch (err) {
                console.error("Error deleting user:", err)
                toast.error(err.response?.data?.message || 'Failed to delete user')
                throw err
            }
        },

        // Bulk delete users
        async bulkDeleteUsers(userIds) {
            try {
                // If API supports bulk delete
                const response = await api.post('/bulkDeleteUsers', { ids: userIds })

                if (response.data.success) {
                    // Remove users from local state
                    this.users = this.users.filter(user => !userIds.includes(user.id))
                    this.user = this.user.filter(user => !userIds.includes(user.id))

                    // Clear selection
                    this.clearSelection()

                    toast.success(`Successfully deleted ${userIds.length} users`)

                    // Refresh data
                    await this.fetchUsers()

                    return true
                }
            } catch (err) {
                // Fallback to individual deletion if bulk API not available
                console.log("Bulk delete not available, falling back to individual deletion")

                let successCount = 0
                let errorCount = 0

                for (const id of userIds) {
                    try {
                        await this.deleteUser(id)
                        successCount++
                    } catch (error) {
                        errorCount++
                    }
                }

                if (successCount > 0) {
                    toast.success(`Successfully deleted ${successCount} users`)
                }
                if (errorCount > 0) {
                    toast.error(`Failed to delete ${errorCount} users`)
                }

                this.clearSelection()
                return successCount > 0
            }
        },
        async updatePassword(data) {
            try {
                const response = await api.post('/update-password', data);
                toast.success(response.data.message);
                useAuthStore().logout();
                window.location.reload();
            } catch (err) {
                console.error("Error updating password:", err);
                toast.error(err.response?.data?.message || "Failed to update password");
            }
        },
        async uploadAvatar(formData) {
            try {
                const response = await api.post('/uploadUserAvatar', formData);
                if (response.data) {
                    toast.success(response.data.message);
                    return response.data.data.file_path;
                }
            } catch (err) {
                console.error("Error uploading avatar:", err);
                toast.error(err.response?.data?.message || "Failed to upload avatar");
            }
        }

    },
});

export default useUserStore;

