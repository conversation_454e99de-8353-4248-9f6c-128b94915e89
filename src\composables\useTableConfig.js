// Simple table configuration helper
export function useTableConfig() {
  
  // Create basic table configuration
  const createBasicConfig = (options = {}) => {
    return {
      // Basic settings
      itemsPerPage: options.itemsPerPage || 10,
      sortable: options.sortable !== false,
      filterable: options.filterable !== false,
      selectable: options.selectable !== false,
      multiSelect: options.multiSelect !== false,
      showPagination: options.showPagination !== false,
      showSearch: options.showSearch !== false,
      exportable: options.exportable !== false,
      clickableRows: options.clickableRows || false,
      serverSide: options.serverSide || false,
      idColumn: options.idColumn || 'id',
      
      // Scrolling (disabled by default for simplicity)
      scrollable: false
    }
  }

  // Create scrollable table configuration
  const createScrollableConfig = (options = {}) => {
    const basic = createBasicConfig(options)
    
    return {
      ...basic,
      scrollable: true,
      scrollConfig: {
        height: options.height || '400px',
        mobileHeight: options.mobileHeight || '300px',
        showBorder: options.showBorder !== false,
        borderStyle: options.borderStyle || 'dashed'
      }
    }
  }

  // Create server-side table configuration
  const createServerConfig = (options = {}) => {
    const basic = createBasicConfig(options)
    
    return {
      ...basic,
      serverSide: true,
      itemsPerPage: options.itemsPerPage || 10
    }
  }

  // Create simple table configuration (minimal features)
  const createSimpleConfig = (options = {}) => {
    return {
      itemsPerPage: options.itemsPerPage || 10,
      sortable: options.sortable !== false,
      filterable: false,
      selectable: false,
      multiSelect: false,
      showPagination: options.showPagination !== false,
      showSearch: options.showSearch !== false,
      exportable: false,
      clickableRows: options.clickableRows || false,
      serverSide: false,
      idColumn: options.idColumn || 'id',
      scrollable: false
    }
  }

  // Create column configuration helpers
  const createColumn = (key, label, options = {}) => {
    return {
      key,
      label: label || key,
      sortable: options.sortable !== false,
      filterable: options.filterable !== false,
      type: options.type || 'text',
      align: options.align || 'left',
      hideOnMobile: options.hideOnMobile || false,
      hideOnPrint: options.hideOnPrint || false,
      hideOnExport: options.hideOnExport || false,
      hideOnTable: options.hideOnTable || false,
      ...options
    }
  }

  // Create ID column
  const createIdColumn = (key = 'id', label = 'ID') => {
    return createColumn(key, label, {
      sortable: true,
      filterable: false,
      type: 'text',
      align: 'left'
    })
  }

  // Create text column
  const createTextColumn = (key, label, options = {}) => {
    return createColumn(key, label, {
      type: 'text',
      ...options
    })
  }

  // Create badge/status column
  const createBadgeColumn = (key, label, badgeColors = {}, options = {}) => {
    const defaultColors = {
      1: 'bg-green-100 text-green-800',
      0: 'bg-red-100 text-red-800',
      'active': 'bg-green-100 text-green-800',
      'inactive': 'bg-red-100 text-red-800',
      'default': 'bg-gray-100 text-gray-800'
    }

    return createColumn(key, label, {
      type: 'badge',
      badgeColors: { ...defaultColors, ...badgeColors },
      ...options
    })
  }

  // Create date column
  const createDateColumn = (key, label, options = {}) => {
    return createColumn(key, label, {
      type: 'date',
      dateFormat: options.dateFormat || 'short',
      ...options
    })
  }

  // Create actions column
  const createActionsColumn = (actions = ['edit', 'delete']) => {
    return createColumn('actions', 'Actions', {
      type: 'actions',
      actions,
      sortable: false,
      filterable: false,
      align: 'center'
    })
  }

  // Preset configurations for common use cases
  const presets = {
    // Basic CRUD table
    crud: (options = {}) => createBasicConfig({
      selectable: true,
      multiSelect: true,
      exportable: true,
      ...options
    }),

    // Simple display table
    display: (options = {}) => createSimpleConfig({
      selectable: false,
      exportable: false,
      ...options
    }),

    // Scrollable table for large datasets
    scrollable: (options = {}) => createScrollableConfig({
      height: '500px',
      mobileHeight: '400px',
      ...options
    }),

    // Server-side table for large datasets
    serverSide: (options = {}) => createServerConfig({
      itemsPerPage: 25,
      ...options
    }),

    // Minimal table
    minimal: (options = {}) => ({
      itemsPerPage: 5,
      sortable: false,
      filterable: false,
      selectable: false,
      multiSelect: false,
      showPagination: false,
      showSearch: false,
      exportable: false,
      clickableRows: false,
      serverSide: false,
      idColumn: 'id',
      scrollable: false,
      ...options
    })
  }

  return {
    // Configuration creators
    createBasicConfig,
    createScrollableConfig,
    createServerConfig,
    createSimpleConfig,
    
    // Column creators
    createColumn,
    createIdColumn,
    createTextColumn,
    createBadgeColumn,
    createDateColumn,
    createActionsColumn,
    
    // Presets
    presets
  }
}
