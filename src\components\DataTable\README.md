# Vue DataTable Component

A comprehensive, reusable Vue 3 data table component built with Tailwind CSS. This component provides all the essential features you need for displaying and managing tabular data across multiple Vue projects.

## Features

### Core Features
- ✅ **Flexible Data Display** - Support for various data types and custom renderers
- ✅ **Sorting** - Multi-column sorting with visual indicators
- ✅ **Filtering** - Global search + column-specific filters
- ✅ **Pagination** - Configurable page sizes with navigation
- ✅ **Selection** - Single/multiple row selection with checkboxes
- ✅ **Actions** - Row-level and bulk actions
- ✅ **Export** - CSV export functionality
- ✅ **Loading States** - Skeleton loading and empty states
- ✅ **Responsive** - Mobile-friendly design with card view
- ✅ **Server-side Pagination** - Laravel pagination format support
- ✅ **Searchable Filters** - Type-to-search in select dropdowns
- ✅ **White Theme** - Clean, professional light design
- ✅ **Customization** - Extensive customization options

### Built-in Cell Types
- **Text** - Default text display
- **Avatar** - User avatars with optional name/email
- **Badge** - Status badges with custom colors
- **Boolean** - True/false indicators
- **Date** - Formatted date display
- **Currency** - Formatted currency values
- **Progress** - Progress bars with percentages
- **Link** - Clickable links
- **Actions** - Action buttons (edit, delete, etc.)

### Filter Types
- **Text** - Simple text search
- **Select** - Searchable dropdown selection with type-to-filter
- **Date Range** - From/to date filtering
- **Number Range** - Min/max number filtering

## Installation

1. Copy the DataTable components to your project:
```
src/components/DataTable/
├── DataTable.vue
├── DataTableHeader.vue
├── DataTableRow.vue
├── DataTablePagination.vue
├── DataTableFilters.vue
├── DataTableExample.vue
├── index.js
└── README.md

src/composables/
└── useDataTable.js
```

2. Import and use in your components:
```vue
<script setup>
import { DataTable } from '@/components/DataTable'

const data = [
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'active' },
  // ... more data
]

const columns = [
  { key: 'id', label: 'ID', sortable: true },
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email', sortable: true },
  { key: 'status', label: 'Status', type: 'badge' }
]

const config = {
  itemsPerPage: 10,
  sortable: true,
  filterable: true,
  selectable: true,
  multiSelect: true,
  showPagination: true,
  showSearch: true,
  exportable: true
}
</script>

<template>
  <DataTable
    :data="data"
    :columns="columns"
    :config="config"
    title="Users Table"
    description="Manage your users"
  />
</template>
```

## Props

### DataTable Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `data` | Array | `[]` | Array of data objects to display |
| `columns` | Array | Required | Column configuration array |
| `title` | String | `''` | Table title |
| `description` | String | `''` | Table description |
| `config` | Object | `{}` | Configuration options |
| `loading` | Boolean | `false` | Show loading state |

### Column Configuration

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `key` | String | Required | Data property key |
| `label` | String | `key` | Column header label |
| `sortable` | Boolean | `true` | Enable sorting |
| `filterable` | Boolean | `true` | Enable filtering |
| `type` | String | `'text'` | Cell type (text, avatar, badge, etc.) |
| `align` | String | `'left'` | Text alignment (left, center, right) |
| `class` | String | `''` | Custom CSS classes |
| `cellClass` | String | `''` | Custom cell CSS classes |
| `formatter` | Function | `null` | Custom value formatter |
| `emptyText` | String | `'-'` | Text for empty values |

### Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `itemsPerPage` | Number | `10` | Items per page |
| `sortable` | Boolean | `true` | Enable sorting |
| `filterable` | Boolean | `true` | Enable filtering |
| `selectable` | Boolean | `false` | Enable row selection |
| `multiSelect` | Boolean | `false` | Enable multiple selection |
| `showPagination` | Boolean | `true` | Show pagination |
| `showSearch` | Boolean | `true` | Show global search |
| `exportable` | Boolean | `false` | Enable CSV export |
| `clickableRows` | Boolean | `false` | Make rows clickable |

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `row-click` | `(row, index)` | Row clicked |
| `selection-change` | `(selectedRows)` | Selection changed |
| `data-change` | `(newData)` | Data modified |

## Slots

### Global Slots
- `actions` - Custom action buttons in header
- `cell-{columnKey}` - Custom cell content for specific columns

### Example Usage with Slots

```vue
<template>
  <DataTable :data="data" :columns="columns">
    <!-- Custom actions -->
    <template #actions="{ selected, hasSelection }">
      <button v-if="hasSelection" @click="bulkDelete(selected)">
        Delete Selected ({{ selected.length }})
      </button>
      <button @click="addNew">Add New</button>
    </template>

    <!-- Custom user cell -->
    <template #cell-user="{ row, value }">
      <div class="flex items-center">
        <img :src="row.avatar" class="w-8 h-8 rounded-full mr-2" />
        <div>
          <div class="font-medium">{{ row.name }}</div>
          <div class="text-sm text-gray-500">{{ row.email }}</div>
        </div>
      </div>
    </template>

    <!-- Custom actions cell -->
    <template #cell-actions="{ row, index }">
      <button @click="edit(row)">Edit</button>
      <button @click="delete(row)">Delete</button>
    </template>
  </DataTable>
</template>
```

## Server-side Pagination

The component automatically detects Laravel pagination format:

```javascript
// Your API response
const response = {
  "success": true,
  "data": {
    "current_page": 1,
    "data": [...], // Your actual data
    "last_page": 5,
    "per_page": 10,
    "total": 50,
    "from": 1,
    "to": 10,
    // ... other Laravel pagination fields
  }
}

// Just pass the data.data to the table
users.value = response.data // Automatically detected as server-side
```

## Responsive Design

### Desktop View
- Full table with all columns
- Horizontal scrolling for wide tables
- Complete sorting and filtering

### Mobile View (< 768px)
- Automatic card layout
- Stacked data presentation
- Touch-friendly interactions
- Hide specific columns with `hideOnMobile: true`

```javascript
const columns = [
  { key: 'name', label: 'Name' },
  { key: 'email', label: 'Email', hideOnMobile: true }, // Hidden on mobile
  { key: 'status', label: 'Status', type: 'badge' }
]
```

## Searchable Select Filters

Select filters now include search functionality:

```javascript
{
  key: 'department',
  label: 'Department',
  filterType: 'select',
  filterOptions: [
    { value: 'engineering', label: 'Engineering' },
    { value: 'marketing', label: 'Marketing' },
    { value: 'sales', label: 'Sales' }
  ]
}
```

Users can:
- Type to search options
- Click dropdown arrow to see all options
- Select from filtered results

## Advanced Examples

See `DataTableExample.vue` for comprehensive examples including:
- Basic table setup
- Advanced cell types
- Custom filtering
- Row actions
- Bulk operations
- Export functionality
- Server-side pagination
- Mobile responsiveness

## Styling

The component uses Tailwind CSS classes and supports dark mode out of the box. You can customize the appearance by:

1. **Custom CSS Classes**: Use the `class` and `cellClass` properties in column configuration
2. **Tailwind Utilities**: Apply Tailwind classes directly
3. **CSS Variables**: Override CSS custom properties for colors
4. **Scoped Styles**: Add component-specific styles

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Vue 3.0+
- Tailwind CSS 3.0+

## Contributing

1. Fork the repository
2. Create your feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - feel free to use in your projects!
