# Quick Start Guide - Vue DataTable Component

## 🚀 Quick Setup

### 1. Basic Usage
```vue
<template>
  <DataTable
    :data="users"
    :columns="columns"
    title="My Data Table"
  />
</template>

<script setup>
import DataTable from '@/components/DataTable/DataTable.vue'

const users = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', status: 'active' }
]

const columns = [
  { key: 'id', label: 'ID' },
  { key: 'name', label: 'Name' },
  { key: 'email', label: 'Email' },
  { key: 'status', label: 'Status', type: 'badge' }
]
</script>
```

### 2. With All Features Enabled
```vue
<template>
  <DataTable
    :data="data"
    :columns="columns"
    :config="config"
    title="Advanced Table"
    description="Full-featured data table"
    @row-click="handleRowClick"
    @selection-change="handleSelection"
  />
</template>

<script setup>
const config = {
  itemsPerPage: 10,
  sortable: true,
  filterable: true,
  selectable: true,
  multiSelect: true,
  showPagination: true,
  showSearch: true,
  exportable: true
}
</script>
```

## 📊 Column Types

### Badge Column
```javascript
{
  key: 'status',
  label: 'Status',
  type: 'badge',
  badgeColors: {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-red-100 text-red-800'
  }
}
```

### Avatar Column
```javascript
{
  key: 'avatar',
  label: 'User',
  type: 'avatar',
  showName: true,
  nameKey: 'name',
  emailKey: 'email'
}
```

### Date Column
```javascript
{
  key: 'created_at',
  label: 'Created',
  type: 'date',
  dateFormat: 'short' // 'short', 'long', 'time'
}
```

### Currency Column
```javascript
{
  key: 'salary',
  label: 'Salary',
  type: 'currency',
  currency: 'USD'
}
```

### Progress Column
```javascript
{
  key: 'completion',
  label: 'Progress',
  type: 'progress'
}
```

### Boolean Column
```javascript
{
  key: 'is_verified',
  label: 'Verified',
  type: 'boolean',
  trueText: 'Verified',
  falseText: 'Unverified'
}
```

## 🔍 Filter Types

### Select Filter
```javascript
{
  key: 'department',
  label: 'Department',
  filterType: 'select',
  filterOptions: [
    { value: 'engineering', label: 'Engineering' },
    { value: 'marketing', label: 'Marketing' }
  ]
}
```

### Date Range Filter
```javascript
{
  key: 'created_at',
  label: 'Created Date',
  type: 'date',
  filterType: 'dateRange'
}
```

### Number Range Filter
```javascript
{
  key: 'salary',
  label: 'Salary',
  type: 'currency',
  filterType: 'numberRange'
}
```

## 🎨 Custom Slots

### Custom Cell Content
```vue
<template #cell-user="{ row, value }">
  <div class="flex items-center">
    <img :src="row.avatar" class="w-8 h-8 rounded-full mr-2" />
    <div>
      <div class="font-medium">{{ row.name }}</div>
      <div class="text-sm text-gray-500">{{ row.email }}</div>
    </div>
  </div>
</template>
```

### Custom Actions
```vue
<template #actions="{ selected, hasSelection }">
  <button v-if="hasSelection" @click="bulkDelete(selected)">
    Delete Selected ({{ selected.length }})
  </button>
  <button @click="addNew">Add New</button>
</template>
```

### Row Actions
```vue
<template #cell-actions="{ row, index }">
  <button @click="edit(row)">Edit</button>
  <button @click="delete(row)">Delete</button>
</template>
```

## ⚙️ Configuration Options

```javascript
const config = {
  // Pagination
  itemsPerPage: 10,
  showPagination: true,
  
  // Sorting
  sortable: true,
  
  // Filtering
  filterable: true,
  showSearch: true,
  
  // Selection
  selectable: true,
  multiSelect: true,
  
  // Features
  exportable: true,
  clickableRows: false
}
```

## 📱 Responsive Design

The table is fully responsive and will:
- Stack columns on mobile devices
- Hide less important columns on smaller screens
- Provide horizontal scrolling when needed
- Adapt pagination controls for mobile

## 🌙 Dark Mode

Dark mode is automatically supported through Tailwind CSS dark mode classes. No additional configuration needed.

## 🔧 Custom Formatting

### Custom Formatter Function
```javascript
{
  key: 'price',
  label: 'Price',
  formatter: (value, row) => {
    return `$${value.toFixed(2)} ${row.currency}`
  }
}
```

### Empty Value Handling
```javascript
{
  key: 'description',
  label: 'Description',
  emptyText: 'No description available'
}
```

## 📤 Export Functionality

Enable CSV export:
```javascript
const config = {
  exportable: true
}
```

Or use the composable directly:
```javascript
import { useDataTable } from '@/components/DataTable'

const { exportToCSV } = useDataTable(data)

// Export filtered data
exportToCSV('my-data.csv')
```

## 🎯 Events

```vue
<DataTable
  @row-click="(row, index) => console.log('Clicked:', row)"
  @selection-change="(selected) => console.log('Selected:', selected)"
  @data-change="(newData) => console.log('Data changed:', newData)"
/>
```

## 💡 Tips

1. **Performance**: For large datasets, consider server-side pagination
2. **Accessibility**: The component includes ARIA labels and keyboard navigation
3. **Customization**: Use Tailwind classes in `class` and `cellClass` properties
4. **Loading States**: Set `:loading="true"` to show skeleton loading
5. **Empty States**: Customize empty state messages through slots

## 🐛 Troubleshooting

### Common Issues

1. **Images not loading**: Ensure `handleImageError` is implemented
2. **Filters not working**: Check column `filterable` property
3. **Sorting not working**: Verify column `sortable` property
4. **Dark mode issues**: Ensure Tailwind dark mode is configured

### Performance Tips

1. Use `v-memo` for large datasets
2. Implement virtual scrolling for 1000+ rows
3. Use server-side filtering/sorting for better performance
4. Debounce search input for better UX
