<template>
  <div class="p-6 space-y-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">DataTable Component Examples</h1>
      <p class="text-gray-600 dark:text-gray-400">Comprehensive examples of the reusable DataTable component with various features.</p>
    </div>

    <!-- Basic Example -->
    <div class="space-y-4">
      <h2 class="text-2xl font-semibold text-gray-900 dark:text-white">Basic Example</h2>
      <DataTable
        :data="basicData"
        :columns="basicColumns"
        title="Users List"
        description="A simple table with basic user information"
        :config="basicConfig"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
      />
    </div>

    <!-- Advanced Example -->
    <div class="space-y-4">
      <h2 class="text-2xl font-semibold text-gray-900 dark:text-white">Advanced Example</h2>
      <DataTable
        :data="advancedData"
        :columns="advancedColumns"
        title="Advanced Users Table"
        description="Table with custom cell types, actions, and advanced features"
        :config="advancedConfig"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
      >
        <!-- Custom action buttons -->
        <template #actions="{ selected, hasSelection }">
          <div class="flex items-center gap-2">
            <button
              v-if="hasSelection"
              @click="bulkDelete(selected)"
              class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:ring-4 focus:ring-red-100"
            >
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"/>
              </svg>
              Delete Selected ({{ selected.length }})
            </button>
            <button
              @click="exportData"
              class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-4 focus:ring-gray-100"
            >
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z"/>
              </svg>
              Export
            </button>
            <button
              @click="addNewUser"
              class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-lg hover:bg-primary-hbr focus:ring-4 focus:ring-primary/20"
            >
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
              </svg>
              Add User
            </button>
          </div>
        </template>

        <!-- Custom cell for user info -->
        <template #cell-user="{ row, value }">
          <div class="flex items-center">
            <div class="flex-shrink-0 h-10 w-10">
              <img
                :src="row.image_url || '/default-avatar.png'"
                :alt="row.name"
                class="h-10 w-10 rounded-full object-cover"
              />
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-gray-900 dark:text-white">{{ row.name }}</div>
              <div class="text-sm text-gray-500 dark:text-gray-400">{{ row.email }}</div>
            </div>
          </div>
        </template>

        <!-- Custom actions for each row -->
        <template #cell-actions="{ row, index }">
          <div class="flex items-center justify-end space-x-2">
            <button
              @click="editUser(row, index)"
              class="text-primary hover:text-primary-hbr text-sm font-medium"
            >
              Edit
            </button>
            <button
              @click="viewUser(row, index)"
              class="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              View
            </button>
            <button
              @click="deleteUser(row, index)"
              class="text-red-600 hover:text-red-800 text-sm font-medium"
            >
              Delete
            </button>
          </div>
        </template>
      </DataTable>
    </div>

    <!-- Configuration Examples -->
    <div class="space-y-4">
      <h2 class="text-2xl font-semibold text-gray-900 dark:text-white">Configuration Options</h2>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Basic Config</h3>
          <pre class="text-sm text-gray-600 dark:text-gray-400 overflow-x-auto"><code>{{ JSON.stringify(basicConfig, null, 2) }}</code></pre>
        </div>
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Advanced Config</h3>
          <pre class="text-sm text-gray-600 dark:text-gray-400 overflow-x-auto"><code>{{ JSON.stringify(advancedConfig, null, 2) }}</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import DataTable from './DataTable.vue'

// Basic example data
const basicData = ref([
  { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'Admin' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'User' },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', role: 'Editor' },
  { id: 4, name: 'Alice Brown', email: '<EMAIL>', role: 'User' },
  { id: 5, name: 'Charlie Wilson', email: '<EMAIL>', role: 'Admin' }
])

const basicColumns = [
  { key: 'id', label: 'ID', sortable: true },
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email', sortable: true },
  { key: 'role', label: 'Role', sortable: true, type: 'badge', badgeColors: {
    admin: 'bg-purple-100 text-purple-800',
    user: 'bg-blue-100 text-blue-800',
    editor: 'bg-green-100 text-green-800'
  }}
]

const basicConfig = {
  itemsPerPage: 5,
  sortable: true,
  filterable: true,
  selectable: true,
  multiSelect: true,
  showPagination: true,
  showSearch: true,
  exportable: true
}

// Advanced example data
const advancedData = ref([
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    image_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    user_position: 'Senior Developer',
    status: 'active',
    created_at: '2023-01-15T10:30:00Z',
    updated_at: '2024-01-15T14:20:00Z',
    salary: 85000,
    progress: 85,
    is_verified: true
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    image_url: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
    user_position: 'Product Manager',
    status: 'active',
    created_at: '2023-02-20T09:15:00Z',
    updated_at: '2024-01-10T11:45:00Z',
    salary: 95000,
    progress: 92,
    is_verified: true
  },
  {
    id: 3,
    name: 'Bob Johnson',
    email: '<EMAIL>',
    image_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
    user_position: 'Designer',
    status: 'inactive',
    created_at: '2023-03-10T16:45:00Z',
    updated_at: '2023-12-05T08:30:00Z',
    salary: 70000,
    progress: 65,
    is_verified: false
  }
])

const advancedColumns = [
  { 
    key: 'user', 
    label: 'User', 
    sortable: false,
    filterable: false
  },
  { 
    key: 'user_position', 
    label: 'Position', 
    sortable: true,
    filterType: 'select',
    filterOptions: [
      { value: 'Senior Developer', label: 'Senior Developer' },
      { value: 'Product Manager', label: 'Product Manager' },
      { value: 'Designer', label: 'Designer' }
    ]
  },
  { 
    key: 'status', 
    label: 'Status', 
    sortable: true, 
    type: 'badge',
    badgeColors: {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-red-100 text-red-800',
      pending: 'bg-yellow-100 text-yellow-800'
    },
    filterType: 'select',
    filterOptions: [
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' },
      { value: 'pending', label: 'Pending' }
    ]
  },
  { 
    key: 'salary', 
    label: 'Salary', 
    sortable: true, 
    type: 'currency',
    currency: 'USD',
    filterType: 'numberRange'
  },
  { 
    key: 'progress', 
    label: 'Progress', 
    sortable: true, 
    type: 'progress'
  },
  { 
    key: 'is_verified', 
    label: 'Verified', 
    sortable: true, 
    type: 'boolean',
    trueText: 'Verified',
    falseText: 'Unverified'
  },
  { 
    key: 'created_at', 
    label: 'Created', 
    sortable: true, 
    type: 'date',
    dateFormat: 'short',
    filterType: 'dateRange'
  },
  { 
    key: 'actions', 
    label: 'Actions', 
    sortable: false,
    filterable: false,
    align: 'right'
  }
]

const advancedConfig = {
  itemsPerPage: 10,
  sortable: true,
  filterable: true,
  selectable: true,
  multiSelect: true,
  showPagination: true,
  showSearch: true,
  exportable: true,
  clickableRows: false
}

// Event handlers
const handleRowClick = (row, index) => {
  console.log('Row clicked:', row, index)
}

const handleSelectionChange = (selectedRows) => {
  console.log('Selection changed:', selectedRows)
}

const bulkDelete = (selectedRows) => {
  console.log('Bulk delete:', selectedRows)
  // Implement bulk delete logic
}

const exportData = () => {
  console.log('Export data')
  // Implement export logic
}

const addNewUser = () => {
  console.log('Add new user')
  // Implement add user logic
}

const editUser = (row, index) => {
  console.log('Edit user:', row, index)
  // Implement edit logic
}

const viewUser = (row, index) => {
  console.log('View user:', row, index)
  // Implement view logic
}

const deleteUser = (row, index) => {
  console.log('Delete user:', row, index)
  // Implement delete logic
}
</script>

<style scoped>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
