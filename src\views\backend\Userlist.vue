<template>
    <div class="p-6">
        <DataTable :data="users" :columns="columns" :config="tableConfig" :loading="loading" title="Users Management"
            description="Manage all users in your system" @row-click="handleRowClick"
            @selection-change="handleSelectionChange" @server-search="handleServerSearch"
            @server-filter="handleServerFilter" @server-sort="handleServerSort" @server-paginate="handleServerPaginate">
            <!-- Custom actions in header -->
            <template #actions="{ selected }">
                <div class="flex items-center gap-2">
                    <button v-if="hasSelection" @click="bulkDelete(selected)"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:ring-4 focus:ring-red-100">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                clip-rule="evenodd" />
                        </svg>
                        Delete Selected ({{ selected.length }})
                    </button>
                    <button @click="addNewUser"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-lg hover:bg-primary-hbr focus:ring-4 focus:ring-primary/20">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                                clip-rule="evenodd" />
                        </svg>
                        Add User
                    </button>
                </div>
            </template>

            <!-- Custom user cell with avatar and info -->
            <template #cell-user="{ row }">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <img :src="row.image_url || '/default-avatar.png'" :alt="row.name"
                            class="h-10 w-10 rounded-full object-cover" @error="handleImageError" />
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ row.name }}</div>
                        <div class="text-sm text-gray-500">{{ row.email }}</div>
                    </div>
                </div>
            </template>

            <!-- Custom actions for each row -->
            <template #cell-actions="{ row, index }">
                <div class="flex items-center justify-end space-x-2">
                    <button @click="editUser(row, index)"
                        class="text-primary hover:text-primary-hbr text-sm font-medium">
                        Edit
                    </button>
                    <button @click="viewUser(row, index)" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View
                    </button>
                    <button @click="deleteUser(row)" class="text-red-600 hover:text-red-800 text-sm font-medium">
                        Delete
                    </button>
                </div>
            </template>
        </DataTable>
    </div>
</template>

<script setup>
import {
    computed,
    onMounted,
} from 'vue'

import { storeToRefs } from 'pinia'

import DataTable from '@/components/DataTable/DataTable.vue'
import useUserStore from '@/stores/user'

// Use the user store
const userStore = useUserStore()

// Get reactive state from store
const { loading } = storeToRefs(userStore)

// Computed properties from store
const users = computed(() => {
    // Return the full pagination object for server-side pagination
    if (userStore.pagination) {
        return userStore.pagination
    }
    // Fallback to users array for client-side
    return userStore.users
})
const hasSelection = computed(() => userStore.hasSelectedUsers)

// Table configuration with server-side pagination
const tableConfig = {
    itemsPerPage: 10,
    sortable: true,
    filterable: true,
    selectable: true,
    multiSelect: true,
    showPagination: true,
    showSearch: true,
    exportable: true,
    clickableRows: false,
    serverSide: true // Enable server-side pagination
}

// Column definitions with dynamic filter options from store
const columns = computed(() => [
    {
        key: 'user',
        label: 'User',
        sortable: false,
        filterable: false
    },
    {
        key: 'user_position',
        label: 'Position',
        sortable: true,
        filterType: 'select',
        filterOptions: userStore.availablePositions
    },
    {
        key: 'status',
        label: 'Status',
        sortable: true,
        type: 'badge',
        badgeColors: {
            active: 'bg-green-100 text-green-800',
            inactive: 'bg-red-100 text-red-800',
            pending: 'bg-yellow-100 text-yellow-800'
        },
        filterType: 'select',
        filterOptions: userStore.availableStatuses
    },
    {
        key: 'created_at',
        label: 'Created',
        sortable: true,
        type: 'date',
        dateFormat: 'short',
        filterType: 'dateRange'
    },
    {
        key: 'updated_at',
        label: 'Last Updated',
        sortable: true,
        type: 'date',
        dateFormat: 'short',
        hideOnMobile: true // Hide this column on mobile for better UX
    },
    {
        key: 'actions',
        label: 'Actions',
        sortable: false,
        filterable: false,
        align: 'right'
    }
])



// Methods using store for server-side operations
const loadUsers = async (params = {}) => {
    try {
        await userStore.fetchUsers(params)
    } catch (error) {
        console.error('Error loading users:', error)
    }
}

// Server-side event handlers
const handleServerSearch = async (payload) => {
    await loadUsers({
        search: payload.search,
        page: payload.page
    })
}

const handleServerFilter = async (payload) => {
    const params = {
        page: payload.page,
        ...payload.filters
    }
    if (payload.column && payload.value) {
        params[payload.column] = payload.value
    }
    await loadUsers(params)
}

const handleServerSort = async (payload) => {
    await loadUsers({
        sortBy: payload.column,
        sortDirection: payload.direction
    })
}

const handleServerPaginate = async (payload) => {
    await loadUsers({
        page: payload.page,
        limit: payload.itemsPerPage,
        search: payload.search,
        sortBy: payload.sortBy,
        sortDirection: payload.sortDirection,
        ...payload.filters
    })
}

const handleRowClick = (row, index) => {
    console.log('Row clicked:', row, index)
    // Navigate to user detail page or open modal
}

const handleSelectionChange = (selectedRows) => {
    userStore.setSelectedUsers(selectedRows)
}

const bulkDelete = async (selectedRows) => {
    if (confirm(`Are you sure you want to delete ${selectedRows.length} users?`)) {
        try {
            const userIds = selectedRows.map(user => user.id)
            await userStore.bulkDeleteUsers(userIds)
        } catch (error) {
            console.error('Error in bulk delete:', error)
        }
    }
}

const addNewUser = () => {
    console.log('Add new user')
    // Navigate to add user page or open modal
}

const editUser = (row, index) => {
    console.log('Edit user:', row, index)
    // Navigate to edit page or open modal
}

const viewUser = (row, index) => {
    console.log('View user:', row, index)
    // Navigate to user detail page or open modal
}

const deleteUser = async (row) => {
    if (confirm(`Are you sure you want to delete ${row.name}?`)) {
        try {
            await userStore.deleteUser(row.id)
        } catch (error) {
            console.error('Error deleting user:', error)
        }
    }
}

const handleImageError = (event) => {
    event.target.src = '/default-avatar.png'
}

// Lifecycle
onMounted(async () => {
    await loadUsers()
})
</script>