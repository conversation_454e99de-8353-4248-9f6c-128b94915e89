<template>
    <div class="p-6">
        <DataTable :data="users" :columns="columns" :config="tableConfig" :loading="loading" title="Users Management"
            description="Manage all users in your system" @row-click="handleRowClick"
            @selection-change="handleSelectionChange" @server-search="handleServerSearch"
            @server-filter="handleServerFilter" @server-sort="handleServerSort" @server-paginate="handleServerPaginate">
            <!-- Custom actions in header -->
            <template #actions="{ selected }">
                <div class="flex items-center gap-2">
                    <!-- Export Dropdown -->
                    <div class="relative" ref="exportDropdown">
                        <button @click="toggleExportDropdown"
                            class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-4 focus:ring-gray-100">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path
                                    d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" />
                            </svg>
                            Export
                            <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>

                        <!-- Export Dropdown Menu -->
                        <div v-if="showExportDropdown"
                            class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                            <div class="py-1">
                                <button @click="exportToCSV"
                                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <svg class="w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm0 2h12v10H4V5z" />
                                    </svg>
                                    Export as CSV
                                </button>
                                <button @click="exportToExcel"
                                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <svg class="w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm0 2h12v10H4V5z" />
                                    </svg>
                                    Export as Excel
                                </button>
                                <button @click="printTable"
                                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <svg class="w-4 h-4 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zM5 14H4v-2h1v2zm1 0v2h8v-2H6zM15 12h1v2h-1v-2z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    Print Table
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Delete Button -->
                    <button v-if="hasSelection" @click="bulkDelete(selected)"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:ring-4 focus:ring-red-100">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                clip-rule="evenodd" />
                        </svg>
                        Delete Selected ({{ selected.length }})
                    </button>

                    <!-- Add User Button -->
                    <button @click="addNewUser"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-lg hover:bg-primary-hbr focus:ring-4 focus:ring-primary/20">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                                clip-rule="evenodd" />
                        </svg>
                        Add User
                    </button>
                </div>
            </template>

            <!-- Custom user cell with avatar and info -->
            <template #cell-user="{ row }">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <img :src="row.image_url || '/default-avatar.png'" :alt="row.name"
                            class="h-10 w-10 rounded-full object-cover" @error="handleImageError" />
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ row.name }}</div>
                        <div class="text-sm text-gray-500">{{ row.email }}</div>
                    </div>
                </div>
            </template>

            <!-- Custom actions for each row -->
            <template #cell-actions="{ row, index }">
                <div class="flex items-center justify-center space-x-1">
                    <!-- View/Details Button -->
                    <button @click="viewUser(row, index)"
                        class="inline-flex items-center justify-center w-8 h-8 text-gray-500 bg-gray-100 rounded-full hover:bg-blue-100 hover:text-blue-600 transition-all duration-200 cursor-pointer group"
                        title="View Details">
                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                    </button>

                    <!-- Edit Button -->
                    <button @click="editUser(row, index)"
                        class="inline-flex items-center justify-center w-8 h-8 text-gray-500 bg-gray-100 rounded-full hover:bg-yellow-100 hover:text-yellow-600 transition-all duration-200 cursor-pointer group"
                        title="Edit User">
                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                    </button>

                    <!-- Delete Button -->
                    <button @click="deleteUser(row)"
                        class="inline-flex items-center justify-center w-8 h-8 text-gray-500 bg-gray-100 rounded-full hover:bg-red-100 hover:text-red-600 transition-all duration-200 cursor-pointer group"
                        title="Delete User">
                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </button>
                </div>
            </template>
        </DataTable>
    </div>
</template>

<script setup>
import {
    computed,
    onMounted,
    onUnmounted,
    ref,
} from 'vue'

import { storeToRefs } from 'pinia'

import DataTable from '@/components/DataTable/DataTable.vue'
import useUserStore from '@/stores/user'

// Use the user store
const userStore = useUserStore()

// Get reactive state from store
const { loading } = storeToRefs(userStore)

// Computed properties from store
const users = computed(() => {
    // Return the full pagination object for server-side pagination
    if (userStore.pagination) {
        return userStore.pagination
    }
    // Fallback to users array for client-side
    return userStore.users
})
const hasSelection = computed(() => userStore.hasSelectedUsers)

// Export dropdown state
const showExportDropdown = ref(false)
const exportDropdown = ref(null)

// Table configuration with server-side pagination
const tableConfig = {
    itemsPerPage: 10,
    sortable: true,
    filterable: true,
    selectable: true,
    multiSelect: true,
    showPagination: true,
    showSearch: true,
    exportable: true,
    clickableRows: false,
    serverSide: true, // Enable server-side pagination
    // Scrolling configuration
    scrollable: true,
    scrollConfig: {
        height: '500px',        // Desktop table height
        mobileHeight: '400px',  // Mobile cards height
        showBorder: true,       // Show visual border
        borderStyle: 'dashed'   // 'dashed', 'solid', 'dotted'
    }
}

// Column definitions with dynamic filter options from store
const columns = computed(() => [
    {
        key: 'user',
        label: 'User',
        sortable: false,
        filterable: false
    },
    {
        key: 'user_position',
        label: 'Position',
        sortable: true,
        filterType: 'select',
        filterOptions: userStore.availablePositions
    },
    {
        key: 'status',
        label: 'Status',
        sortable: true,
        type: 'badge',
        badgeColors: {
            1: 'bg-green-100 text-green-800',    // Active
            0: 'bg-red-100 text-red-800',       // Inactive
            '1': 'bg-green-100 text-green-800',  // Active (string)
            '0': 'bg-red-100 text-red-800',     // Inactive (string)
            active: 'bg-green-100 text-green-800',
            inactive: 'bg-red-100 text-red-800'
        },
        filterType: 'select',
        filterOptions: [
            { value: '1', label: 'Active' },
            { value: '0', label: 'Inactive' }
        ],
        formatter: (value) => {
            // Convert numeric status to text
            if (value === 1 || value === '1') return 'Active'
            if (value === 0 || value === '0') return 'Inactive'
            return value
        }
    },
    {
        key: 'created_at',
        label: 'Created',
        sortable: true,
        type: 'date',
        dateFormat: 'short',
        filterType: 'dateRange'
    },
    {
        key: 'updated_at',
        label: 'Last Updated',
        sortable: true,
        type: 'date',
        dateFormat: 'short',
        hideOnMobile: true // Hide this column on mobile for better UX
    },
    {
        key: 'actions',
        label: 'Actions',
        sortable: false,
        filterable: false,
        align: 'center',
        type: 'actions'
    }
])



// Methods using store for server-side operations
const loadUsers = async (params = {}) => {
    try {
        await userStore.fetchUsers(params)
    } catch (error) {
        console.error('Error loading users:', error)
    }
}

// Server-side event handlers
const handleServerSearch = async (payload) => {
    await loadUsers({
        search: payload.search,
        page: payload.page
    })
}

const handleServerFilter = async (payload) => {
    const params = {
        page: payload.page,
        ...payload.filters
    }
    if (payload.column && payload.value) {
        params[payload.column] = payload.value
    }
    await loadUsers(params)
}

const handleServerSort = async (payload) => {
    await loadUsers({
        sortBy: payload.column,
        sortDirection: payload.direction
    })
}

const handleServerPaginate = async (payload) => {
    await loadUsers({
        page: payload.page,
        limit: payload.itemsPerPage,
        search: payload.search,
        sortBy: payload.sortBy,
        sortDirection: payload.sortDirection,
        ...payload.filters
    })
}

const handleRowClick = (row, index) => {
    console.log('Row clicked:', row, index)
    // Navigate to user detail page or open modal
}

const handleSelectionChange = (selectedRows) => {
    userStore.setSelectedUsers(selectedRows)
}

const bulkDelete = async (selectedRows) => {
    if (confirm(`Are you sure you want to delete ${selectedRows.length} users?`)) {
        try {
            const userIds = selectedRows.map(user => user.id)
            await userStore.bulkDeleteUsers(userIds)
        } catch (error) {
            console.error('Error in bulk delete:', error)
        }
    }
}

const addNewUser = () => {
    console.log('Add new user')
    // Navigate to add user page or open modal
}

// Export dropdown methods
const toggleExportDropdown = () => {
    showExportDropdown.value = !showExportDropdown.value
}

const closeExportDropdown = () => {
    showExportDropdown.value = false
}

// Get export data
const getExportData = () => {
    return users.value?.data || users.value || []
}

// Export to CSV
const exportToCSV = () => {
    const dataToExport = getExportData()
    const headers = ['Name', 'Email', 'Position', 'Status', 'Created', 'Updated']
    const csvContent = [
        headers.join(','),
        ...dataToExport.map(user => [
            `"${user.name || ''}"`,
            `"${user.email || ''}"`,
            `"${user.user_position || ''}"`,
            `"${user.status === 1 || user.status === '1' ? 'Active' : 'Inactive'}"`,
            `"${user.created_at ? new Date(user.created_at).toLocaleDateString() : ''}"`,
            `"${user.updated_at ? new Date(user.updated_at).toLocaleDateString() : ''}"`
        ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `users-export-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    closeExportDropdown()
}

// Export to Excel
const exportToExcel = () => {
    const dataToExport = getExportData()

    // Create Excel-compatible content with tab separators
    const headers = ['Name', 'Email', 'Position', 'Status', 'Created', 'Updated']
    const excelContent = [
        headers.join('\t'),
        ...dataToExport.map(user => [
            user.name || '',
            user.email || '',
            user.user_position || '',
            user.status === 1 || user.status === '1' ? 'Active' : 'Inactive',
            user.created_at ? new Date(user.created_at).toLocaleDateString() : '',
            user.updated_at ? new Date(user.updated_at).toLocaleDateString() : ''
        ].join('\t'))
    ].join('\n')

    const blob = new Blob([excelContent], { type: 'application/vnd.ms-excel' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `users-export-${new Date().toISOString().split('T')[0]}.xls`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    closeExportDropdown()
}

// Print table
const printTable = () => {
    const dataToExport = getExportData()

    // Create printable HTML
    const printContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Users Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { color: #333; text-align: center; margin-bottom: 30px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
                th { background-color: #f8f9fa; font-weight: bold; }
                tr:nth-child(even) { background-color: #f8f9fa; }
                .print-date { text-align: center; margin-top: 20px; color: #666; }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <h1>Users Management Report</h1>
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Position</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Last Updated</th>
                    </tr>
                </thead>
                <tbody>
                    ${dataToExport.map(user => `
                        <tr>
                            <td>${user.name || ''}</td>
                            <td>${user.email || ''}</td>
                            <td>${user.user_position || ''}</td>
                            <td>${user.status === 1 || user.status === '1' ? 'Active' : 'Inactive'}</td>
                            <td>${user.created_at ? new Date(user.created_at).toLocaleDateString() : ''}</td>
                            <td>${user.updated_at ? new Date(user.updated_at).toLocaleDateString() : ''}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            <div class="print-date">
                Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
            </div>
        </body>
        </html>
    `

    // Open print window
    const printWindow = window.open('', '_blank')
    printWindow.document.write(printContent)
    printWindow.document.close()
    printWindow.focus()
    printWindow.print()
    printWindow.close()
    closeExportDropdown()
}

const editUser = (row, index) => {
    console.log('Edit user:', row, index)
    // Navigate to edit page or open modal
}

const viewUser = (row, index) => {
    console.log('View user:', row, index)
    // Navigate to user detail page or open modal
}

const deleteUser = async (row) => {
    if (confirm(`Are you sure you want to delete ${row.name}?`)) {
        try {
            await userStore.deleteUser(row.id)
        } catch (error) {
            console.error('Error deleting user:', error)
        }
    }
}

const handleImageError = (event) => {
    event.target.src = '/public/images/file/code.png'
}

// Click outside handler for export dropdown
const handleClickOutside = (event) => {
    if (exportDropdown.value && !exportDropdown.value.contains(event.target)) {
        closeExportDropdown()
    }
}

// Lifecycle
onMounted(async () => {
    await loadUsers()
    document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
})
</script>