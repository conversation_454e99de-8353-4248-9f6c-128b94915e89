import { ref } from 'vue'

export function useExport() {
  const showExportDropdown = ref(false)
  const exportDropdown = ref(null)

  // Export dropdown methods
  const toggleExportDropdown = () => {
    showExportDropdown.value = !showExportDropdown.value
  }

  const closeExportDropdown = () => {
    showExportDropdown.value = false
  }

  // Dynamic export function
  const exportData = (config) => {
    const {
      data = [],
      selectedData = [],
      columns = [],
      filename = 'export',
      format = 'csv', // 'csv', 'excel'
      exportSelected = false
    } = config

    // Determine what data to export
    const dataToExport = exportSelected && selectedData.length > 0
      ? selectedData
      : data

    // Filter exportable columns
    const exportableColumns = columns.filter(col =>
      col.key !== 'actions' &&
      !col.hideOnExport &&
      col.key !== 'selection'
    )

    // Generate export data
    const exportContent = generateExportContent(dataToExport, exportableColumns, format)

    // Download file
    downloadFile(exportContent, filename, format)
    closeExportDropdown()
  }

  // Generate export content based on format
  const generateExportContent = (data, columns, format) => {
    const headers = columns.map(col => col.label || col.key)
    const rows = data.map(row =>
      columns.map(col => formatExportValue(getCellValue(row, col), col))
    )

    switch (format) {
      case 'csv':
        return generateCSV(headers, rows)
      case 'excel':
        return generateExcel(headers, rows)
      default:
        return generateCSV(headers, rows)
    }
  }

  // Get cell value with nested property support
  const getCellValue = (row, column) => {
    const keys = column.key.split('.')
    let value = row

    for (const key of keys) {
      value = value?.[key]
      if (value === undefined || value === null) break
    }

    return value
  }

  // Format value for export
  const formatExportValue = (value, column) => {
    if (value === null || value === undefined) {
      return ''
    }

    // Apply formatter if exists
    if (column.formatter && typeof column.formatter === 'function') {
      return column.formatter(value)
    }

    // Handle different column types
    switch (column.type) {
      case 'date':
        return formatDate(value, column.dateFormat)
      case 'currency':
        return formatCurrency(value, column.currency)
      case 'badge':
        return value // Just return text for export
      case 'boolean':
        return value ? 'Yes' : 'No'
      case 'array':
        return Array.isArray(value) ? value.join(', ') : value
      default:
        return String(value)
    }
  }

  // Format date helper
  const formatDate = (value, format = 'short') => {
    if (!value) return ''
    const date = new Date(value)
    if (isNaN(date.getTime())) return value

    switch (format) {
      case 'short':
        return date.toLocaleDateString()
      case 'long':
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      case 'datetime':
        return date.toLocaleString()
      default:
        return date.toLocaleDateString()
    }
  }

  // Format currency helper
  const formatCurrency = (value, currency = 'USD') => {
    if (!value && value !== 0) return ''
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(value)
  }

  // Generate CSV content
  const generateCSV = (headers, rows) => {
    const csvHeaders = headers.map(header => `"${header}"`).join(',')
    const csvRows = rows.map(row =>
      row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
    )

    return [csvHeaders, ...csvRows].join('\n')
  }

  // Generate Excel content (tab-separated)
  const generateExcel = (headers, rows) => {
    const excelHeaders = headers.join('\t')
    const excelRows = rows.map(row => row.join('\t'))

    return [excelHeaders, ...excelRows].join('\n')
  }

  // Download file
  const downloadFile = (content, filename, format) => {
    const mimeTypes = {
      csv: 'text/csv;charset=utf-8;',
      excel: 'application/vnd.ms-excel'
    }

    const extensions = {
      csv: 'csv',
      excel: 'xls'
    }

    const blob = new Blob([content], { type: mimeTypes[format] })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)

    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}-${getDateString()}.${extensions[format]}`)
    link.style.visibility = 'hidden'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Get date string for filename
  const getDateString = () => {
    return new Date().toISOString().split('T')[0]
  }

  // Click outside handler
  const handleClickOutside = (event) => {
    if (exportDropdown.value && !exportDropdown.value.contains(event.target)) {
      closeExportDropdown()
    }
  }

  // Quick export methods for convenience
  const exportToCSV = (config) => {
    exportData({ ...config, format: 'csv' })
  }

  const exportToExcel = (config) => {
    exportData({ ...config, format: 'excel' })
  }

  const exportSelected = (config) => {
    exportData({ ...config, exportSelected: true })
  }

  const exportAll = (config) => {
    exportData({ ...config, exportSelected: false })
  }

  return {
    // State
    showExportDropdown,
    exportDropdown,

    // Methods
    toggleExportDropdown,
    closeExportDropdown,
    exportData,
    exportToCSV,
    exportToExcel,
    exportSelected,
    exportAll,
    handleClickOutside
  }
}
