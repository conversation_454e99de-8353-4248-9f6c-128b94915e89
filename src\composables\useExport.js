import { ref } from 'vue'

export function useExport() {
  const showExportDropdown = ref(false)
  const exportDropdown = ref(null)

  // Export dropdown methods
  const toggleExportDropdown = () => {
    showExportDropdown.value = !showExportDropdown.value
  }

  const closeExportDropdown = () => {
    showExportDropdown.value = false
  }

  // Format user data for export
  const formatUserData = (users) => {
    return users.map(user => ({
      name: user.name || '',
      email: user.email || '',
      position: user.user_position || '',
      status: user.status === 1 || user.status === '1' ? 'Active' : 'Inactive',
      created: user.created_at ? new Date(user.created_at).toLocaleDateString() : '',
      updated: user.updated_at ? new Date(user.updated_at).toLocaleDateString() : ''
    }))
  }

  // Export to CSV
  const exportToCSV = (data, filename = 'users-export') => {
    const formattedData = formatUserData(data)
    const headers = ['Name', 'Email', 'Position', 'Status', 'Created', 'Updated']
    
    const csvContent = [
      headers.join(','),
      ...formattedData.map(user => [
        `"${user.name}"`,
        `"${user.email}"`,
        `"${user.position}"`,
        `"${user.status}"`,
        `"${user.created}"`,
        `"${user.updated}"`
      ].join(','))
    ].join('\n')

    downloadFile(csvContent, `${filename}-${getDateString()}.csv`, 'text/csv;charset=utf-8;')
    closeExportDropdown()
  }

  // Export to Excel
  const exportToExcel = (data, filename = 'users-export') => {
    const formattedData = formatUserData(data)
    const headers = ['Name', 'Email', 'Position', 'Status', 'Created', 'Updated']
    
    const excelContent = [
      headers.join('\t'),
      ...formattedData.map(user => [
        user.name,
        user.email,
        user.position,
        user.status,
        user.created,
        user.updated
      ].join('\t'))
    ].join('\n')

    downloadFile(excelContent, `${filename}-${getDateString()}.xls`, 'application/vnd.ms-excel')
    closeExportDropdown()
  }

  // Print functionality with options
  const printData = (data, options = {}) => {
    const {
      title = 'Users Management Report',
      showSelectedOnly = false,
      selectedCount = 0
    } = options

    const formattedData = formatUserData(data)
    const subtitle = showSelectedOnly 
      ? `Selected Users (${selectedCount} of ${data.length})`
      : `All Users (${data.length} total)`

    const printContent = generatePrintHTML(formattedData, title, subtitle)
    
    const printWindow = window.open('', '_blank')
    printWindow.document.write(printContent)
    printWindow.document.close()
    printWindow.focus()
    printWindow.print()
    printWindow.close()
    closeExportDropdown()
  }

  // Helper functions
  const downloadFile = (content, filename, mimeType) => {
    const blob = new Blob([content], { type: mimeType })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const getDateString = () => {
    return new Date().toISOString().split('T')[0]
  }

  const generatePrintHTML = (data, title, subtitle) => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
          <title>${title}</title>
          <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              h1 { color: #333; margin-bottom: 10px; }
              .subtitle { color: #666; font-size: 14px; margin-bottom: 20px; }
              table { width: 100%; border-collapse: collapse; margin-top: 20px; }
              th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
              th { background-color: #f8f9fa; font-weight: bold; }
              tr:nth-child(even) { background-color: #f8f9fa; }
              .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
              @media print {
                  body { margin: 0; }
                  .no-print { display: none; }
              }
          </style>
      </head>
      <body>
          <div class="header">
              <h1>${title}</h1>
              <div class="subtitle">${subtitle}</div>
          </div>
          <table>
              <thead>
                  <tr>
                      <th>Name</th>
                      <th>Email</th>
                      <th>Position</th>
                      <th>Status</th>
                      <th>Created</th>
                      <th>Last Updated</th>
                  </tr>
              </thead>
              <tbody>
                  ${data.map(user => `
                      <tr>
                          <td>${user.name}</td>
                          <td>${user.email}</td>
                          <td>${user.position}</td>
                          <td>${user.status}</td>
                          <td>${user.created}</td>
                          <td>${user.updated}</td>
                      </tr>
                  `).join('')}
              </tbody>
          </table>
          <div class="footer">
              Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
          </div>
      </body>
      </html>
    `
  }

  // Click outside handler
  const handleClickOutside = (event) => {
    if (exportDropdown.value && !exportDropdown.value.contains(event.target)) {
      closeExportDropdown()
    }
  }

  return {
    // State
    showExportDropdown,
    exportDropdown,
    
    // Methods
    toggleExportDropdown,
    closeExportDropdown,
    exportToCSV,
    exportToExcel,
    printData,
    handleClickOutside
  }
}
