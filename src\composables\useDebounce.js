import { ref, watch } from 'vue'

export function useDebounce(initialValue = '', delay = 500) {
  const value = ref(initialValue)
  const debouncedValue = ref(initialValue)
  let timeoutId = null

  // Watch for value changes and debounce them
  watch(value, (newValue) => {
    // Clear existing timeout
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    // Set new timeout
    timeoutId = setTimeout(() => {
      debouncedValue.value = newValue
    }, delay)
  })

  // Method to immediately update debounced value (for programmatic updates)
  const setImmediateValue = (newValue) => {
    value.value = newValue
    debouncedValue.value = newValue
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }

  // Method to clear the debounce timeout
  const clearDebounce = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }

  return {
    value,           // The immediate value (updates on every keystroke)
    debouncedValue,  // The debounced value (updates after delay)
    setImmediateValue,
    clearDebounce
  }
}

// Specific composable for search functionality
export function useDebouncedSearch(initialValue = '', delay = 800) {
  const searchInput = ref(initialValue)
  const searchQuery = ref(initialValue)
  const isSearching = ref(false)
  let timeoutId = null

  // Watch for search input changes and debounce them
  watch(searchInput, (newValue) => {
    // Show searching indicator immediately
    isSearching.value = true

    // Clear existing timeout
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    // If search is empty, update immediately
    if (!newValue.trim()) {
      searchQuery.value = ''
      isSearching.value = false
      return
    }

    // Set new timeout for non-empty searches
    timeoutId = setTimeout(() => {
      searchQuery.value = newValue
      isSearching.value = false
    }, delay)
  })

  // Method to immediately trigger search (for programmatic searches)
  const triggerSearch = (query = searchInput.value) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    searchInput.value = query
    searchQuery.value = query
    isSearching.value = false
  }

  // Method to clear search
  const clearSearch = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    searchInput.value = ''
    searchQuery.value = ''
    isSearching.value = false
  }

  return {
    searchInput,    // The immediate input value (updates on every keystroke)
    searchQuery,    // The debounced search query (triggers actual search)
    isSearching,    // Loading indicator while waiting for debounce
    triggerSearch,  // Method to immediately trigger search
    clearSearch     // Method to clear search
  }
}
