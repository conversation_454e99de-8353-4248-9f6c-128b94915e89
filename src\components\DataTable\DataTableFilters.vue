<template>
  <div class="space-y-4">
    <!-- Global Search -->
    <div v-if="config.showSearch" class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div class="relative flex-1 max-w-md">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
        <input type="text" :value="globalFilter" @input="$emit('update:global-filter', $event.target.value)"
          placeholder="Search all columns..."
          class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent sm:text-sm" />
        <div v-if="globalFilter" class="absolute inset-y-0 right-0 pr-3 flex items-center">
          <button @click="$emit('update:global-filter', '')" class="text-gray-400 hover:text-gray-600">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Filter Toggle and Clear -->
      <div class="flex items-center space-x-2">
        <button v-if="config.filterable && filterableColumns.length > 0" @click="showColumnFilters = !showColumnFilters"
          :class="[
            'inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary',
            hasActiveFilters ? 'ring-2 ring-primary' : ''
          ]">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
          </svg>
          Filters
          <span v-if="activeFilterCount > 0"
            class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-primary rounded-full">
            {{ activeFilterCount }}
          </span>
        </button>

        <button v-if="hasActiveFilters" @click="clearAllFilters"
          class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
          Clear All
        </button>
      </div>
    </div>

    <!-- Column Filters -->
    <div v-if="config.filterable && showColumnFilters && filterableColumns.length > 0"
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
      <div v-for="column in filterableColumns" :key="column.key" class="space-y-1">
        <label class="block text-sm font-medium text-gray-700">
          {{ column.label || column.key }}
        </label>

        <!-- Select Filter with Search -->
        <div v-if="column.filterType === 'select'" class="relative">
          <div class="relative">
            <input type="text" :value="getSelectDisplayValue(column.key, column.filterOptions)"
              @input="handleSelectSearch(column.key, $event.target.value, column.filterOptions)"
              @focus="openSelectDropdown(column.key)" @blur="closeSelectDropdown(column.key)"
              :placeholder="`Search ${column.label || column.key}...`"
              class="block w-full px-3 py-2 pr-8 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent sm:text-sm" />
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>

          <!-- Dropdown Options -->
          <div v-if="selectDropdowns[column.key]"
            class="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
            <div @mousedown="selectOption(column.key, '')"
              class="px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm border-b border-gray-100">
              <span class="text-gray-500">All {{ column.label || column.key }}</span>
            </div>
            <div v-for="option in getFilteredSelectOptions(column.key, column.filterOptions)" :key="option.value"
              @mousedown="selectOption(column.key, option.value)"
              class="px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm"
              :class="{ 'bg-blue-50 text-blue-700': columnFilters[column.key] === option.value }">
              {{ option.label }}
            </div>
            <div v-if="getFilteredSelectOptions(column.key, column.filterOptions).length === 0"
              class="px-3 py-2 text-sm text-gray-500">
              No options found
            </div>
          </div>
        </div>

        <!-- Date Range Filter -->
        <div v-else-if="column.filterType === 'dateRange'" class="space-y-2">
          <input type="date" :value="getDateRangeValue(column.key, 'from')"
            @change="updateDateRangeFilter(column.key, 'from', $event.target.value)" placeholder="From date"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent sm:text-sm" />
          <input type="date" :value="getDateRangeValue(column.key, 'to')"
            @change="updateDateRangeFilter(column.key, 'to', $event.target.value)" placeholder="To date"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent sm:text-sm" />
        </div>

        <!-- Number Range Filter -->
        <div v-else-if="column.filterType === 'numberRange'" class="space-y-2">
          <input type="number" :value="getNumberRangeValue(column.key, 'min')"
            @input="updateNumberRangeFilter(column.key, 'min', $event.target.value)"
            :placeholder="`Min ${column.label || column.key}`"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent sm:text-sm" />
          <input type="number" :value="getNumberRangeValue(column.key, 'max')"
            @input="updateNumberRangeFilter(column.key, 'max', $event.target.value)"
            :placeholder="`Max ${column.label || column.key}`"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent sm:text-sm" />
        </div>

        <!-- Text Filter (default) -->
        <div v-else class="relative">
          <input type="text" :value="columnFilters[column.key] || ''"
            @input="updateColumnFilter(column.key, $event.target.value)"
            :placeholder="`Filter ${column.label || column.key}...`"
            class="block w-full px-3 py-2 pr-8 border border-gray-300 rounded-md shadow-sm bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent sm:text-sm" />
          <div v-if="columnFilters[column.key]" class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button @click="updateColumnFilter(column.key, '')" class="text-gray-400 hover:text-gray-600">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  computed,
  ref,
} from 'vue'

// Props
const props = defineProps({
  globalFilter: {
    type: String,
    default: ''
  },
  columnFilters: {
    type: Object,
    default: () => ({})
  },
  columns: {
    type: Array,
    required: true
  },
  config: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:global-filter', 'update:column-filter', 'clear-filters'])

// State
const showColumnFilters = ref(false)
const selectDropdowns = ref({})
const selectSearchTerms = ref({})

// Computed
const filterableColumns = computed(() => {
  return props.columns.filter(column => column.filterable !== false)
})

const hasActiveFilters = computed(() => {
  return props.globalFilter || Object.values(props.columnFilters).some(filter => filter)
})

const activeFilterCount = computed(() => {
  return Object.values(props.columnFilters).filter(filter => filter).length
})

// Methods
const updateColumnFilter = (column, value) => {
  emit('update:column-filter', column, value)
}

const clearAllFilters = () => {
  emit('clear-filters')
}

const getDateRangeValue = (column, type) => {
  const filter = props.columnFilters[column]
  if (filter && typeof filter === 'object') {
    return filter[type] || ''
  }
  return ''
}

const updateDateRangeFilter = (column, type, value) => {
  const currentFilter = props.columnFilters[column] || {}
  const newFilter = { ...currentFilter, [type]: value }

  // Remove empty values
  if (!newFilter.from && !newFilter.to) {
    emit('update:column-filter', column, '')
  } else {
    emit('update:column-filter', column, newFilter)
  }
}

const getNumberRangeValue = (column, type) => {
  const filter = props.columnFilters[column]
  if (filter && typeof filter === 'object') {
    return filter[type] || ''
  }
  return ''
}

const updateNumberRangeFilter = (column, type, value) => {
  const currentFilter = props.columnFilters[column] || {}
  const newFilter = { ...currentFilter, [type]: value }

  // Remove empty values
  if (!newFilter.min && !newFilter.max) {
    emit('update:column-filter', column, '')
  } else {
    emit('update:column-filter', column, newFilter)
  }
}

// Searchable select methods
const getSelectDisplayValue = (columnKey, options) => {
  const selectedValue = props.columnFilters[columnKey]
  if (!selectedValue) return ''

  const option = options?.find(opt => opt.value === selectedValue)
  return option ? option.label : selectedValue
}

const handleSelectSearch = (columnKey, searchTerm, options) => {
  selectSearchTerms.value[columnKey] = searchTerm
  selectDropdowns.value[columnKey] = true

  // If exact match found, select it
  const exactMatch = options?.find(opt =>
    opt.label.toLowerCase() === searchTerm.toLowerCase()
  )
  if (exactMatch) {
    emit('update:column-filter', columnKey, exactMatch.value)
  } else if (!searchTerm) {
    emit('update:column-filter', columnKey, '')
  }
}

const openSelectDropdown = (columnKey) => {
  selectDropdowns.value[columnKey] = true
}

const closeSelectDropdown = (columnKey) => {
  setTimeout(() => {
    selectDropdowns.value[columnKey] = false
  }, 150) // Delay to allow click events
}

const selectOption = (columnKey, value) => {
  emit('update:column-filter', columnKey, value)
  selectDropdowns.value[columnKey] = false
  selectSearchTerms.value[columnKey] = ''
}

const getFilteredSelectOptions = (columnKey, options) => {
  const searchTerm = selectSearchTerms.value[columnKey]
  if (!searchTerm || !options) return options || []

  return options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  )
}
</script>

<style scoped>
/* Custom filter styles */
.filter-container {
  transition: all 0.3s ease-in-out;
}
</style>
