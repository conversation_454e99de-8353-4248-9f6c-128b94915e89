<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Print Options</h3>
      </div>
      
      <div class="px-6 py-4 space-y-4">
        <!-- Data Selection -->
        <div>
          <label class="text-sm font-medium text-gray-700 block mb-2">Data to Print</label>
          <div class="space-y-2">
            <label class="flex items-center">
              <input 
                type="radio" 
                v-model="options.printAll" 
                :value="true"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              >
              <span class="ml-2 text-sm text-gray-700">
                All Data ({{ totalCount }} items)
              </span>
            </label>
            <label class="flex items-center" v-if="selectedCount > 0">
              <input 
                type="radio" 
                v-model="options.printAll" 
                :value="false"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              >
              <span class="ml-2 text-sm text-gray-700">
                Selected Only ({{ selectedCount }} items)
              </span>
            </label>
          </div>
        </div>

        <!-- Include Filters -->
        <div v-if="hasFilters">
          <label class="flex items-center">
            <input 
              type="checkbox" 
              v-model="options.includeFilters"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            >
            <span class="ml-2 text-sm text-gray-700">Include filter information</span>
          </label>
        </div>

        <!-- Paper Size -->
        <div>
          <label class="text-sm font-medium text-gray-700 block mb-2">Paper Size</label>
          <select 
            v-model="options.paperSize"
            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="A4">A4</option>
            <option value="Letter">Letter</option>
            <option value="Legal">Legal</option>
          </select>
        </div>

        <!-- Orientation -->
        <div>
          <label class="text-sm font-medium text-gray-700 block mb-2">Orientation</label>
          <div class="flex space-x-4">
            <label class="flex items-center">
              <input 
                type="radio" 
                v-model="options.orientation" 
                value="portrait"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              >
              <span class="ml-2 text-sm text-gray-700">Portrait</span>
            </label>
            <label class="flex items-center">
              <input 
                type="radio" 
                v-model="options.orientation" 
                value="landscape"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              >
              <span class="ml-2 text-sm text-gray-700">Landscape</span>
            </label>
          </div>
        </div>

        <!-- Font Size -->
        <div>
          <label class="text-sm font-medium text-gray-700 block mb-2">Font Size</label>
          <select 
            v-model="options.fontSize"
            class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="small">Small</option>
            <option value="medium">Medium</option>
            <option value="large">Large</option>
          </select>
        </div>
      </div>

      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button 
          @click="$emit('close')"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button 
          @click="handlePrint"
          class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500"
        >
          Print
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  totalCount: {
    type: Number,
    default: 0
  },
  selectedCount: {
    type: Number,
    default: 0
  },
  hasFilters: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'print'])

const options = ref({
  printAll: true,
  includeFilters: true,
  paperSize: 'A4',
  orientation: 'portrait',
  fontSize: 'medium'
})

// Auto-select "Selected Only" if no items are selected for "All Data"
watch(() => props.selectedCount, (newCount) => {
  if (newCount === 0) {
    options.value.printAll = true
  }
})

const handlePrint = () => {
  emit('print', { ...options.value })
}
</script>
