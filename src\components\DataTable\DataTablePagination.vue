<template>
  <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <!-- Items per page and info -->
      <div class="flex flex-col sm:flex-row sm:items-center gap-4">
        <div class="flex items-center space-x-2">
          <label class="text-sm text-gray-700 whitespace-nowrap">Show:</label>
          <select :value="itemsPerPage" @change="$emit('update:items-per-page', parseInt($event.target.value))"
            class="border border-gray-300 rounded-md px-3 py-1.5 text-sm bg-white text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[70px]">
            <option v-for="option in itemsPerPageOptions" :key="option" :value="option"
              :selected="option === itemsPerPage">
              {{ option }}
            </option>
          </select>
          <span class="text-sm text-gray-700 whitespace-nowrap">per page</span>
        </div>

        <!-- Results info -->
        <div class="text-sm text-gray-700 whitespace-nowrap">
          Showing {{ startItem }} to {{ endItem }} of {{ totalItems }} results
        </div>
      </div>

      <!-- Pagination controls -->
      <div class="hidden sm:flex items-center space-x-1">
        <!-- Previous button -->
        <button :disabled="currentPage === 1" @click="$emit('prev-page')" :class="[
          'relative inline-flex items-center px-3 py-2 text-sm font-medium rounded-l-md border transition-colors',
          currentPage === 1
            ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
            : 'bg-white text-gray-500 border-gray-300 hover:bg-gray-50'
        ]">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
              clip-rule="evenodd" />
          </svg>
        </button>

        <!-- Page numbers -->
        <template v-for="page in visiblePages" :key="page">
          <button v-if="page !== '...'" :class="[
            'relative inline-flex items-center px-4 py-2 text-sm font-medium border transition-colors cursor-pointer',
            page === currentPage
              ? 'bg-blue-600 text-white border-blue-600 z-10'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          ]" @click="$emit('update:current-page', page)">
            {{ page }}
          </button>
          <span v-else
            class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300">
            ...
          </span>
        </template>

        <!-- Next button -->
        <button :disabled="currentPage === totalPages" @click="$emit('next-page')" :class="[
          'relative inline-flex items-center px-3 py-2 text-sm font-medium rounded-r-md border transition-colors',
          currentPage === totalPages
            ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
            : 'bg-white text-gray-500 border-gray-300 hover:bg-gray-50'
        ]">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
              clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <!-- Mobile pagination (compact) -->
      <div class="flex sm:hidden items-center justify-center space-x-2">
        <button :disabled="currentPage === 1" @click="$emit('prev-page')" :class="[
          'inline-flex items-center px-3 py-2 text-sm font-medium rounded-md border transition-colors',
          currentPage === 1
            ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
            : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
        ]">
          <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
              clip-rule="evenodd" />
          </svg>
          Prev
        </button>

        <div class="flex items-center space-x-1">
          <template v-for="page in visiblePages.slice(0, 5)" :key="page">
            <button v-if="page !== '...'" :class="[
              'inline-flex items-center justify-center w-10 h-10 text-sm font-medium rounded-md border transition-colors',
              page === currentPage
                ? 'bg-blue-600 text-white border-blue-600'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
            ]" @click="$emit('update:current-page', page)">
              {{ page }}
            </button>
            <span v-else class="inline-flex items-center justify-center w-10 h-10 text-sm text-gray-500">
              ...
            </span>
          </template>
        </div>

        <button :disabled="currentPage === totalPages" @click="$emit('next-page')" :class="[
          'inline-flex items-center px-3 py-2 text-sm font-medium rounded-md border transition-colors',
          currentPage === totalPages
            ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
            : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
        ]">
          Next
          <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
              clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </div>


  </div>
</template>

<script setup>
import {
  computed,
  onMounted,
  onUnmounted,
  ref,
} from 'vue'

// Props
const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  totalPages: {
    type: Number,
    required: true
  },
  totalItems: {
    type: Number,
    required: true
  },
  itemsPerPage: {
    type: Number,
    required: true
  },
  itemsPerPageOptions: {
    type: Array,
    default: () => [5, 10, 25, 50, 100]
  },
  maxVisiblePages: {
    type: Number,
    default: 7
  }
})

// Emits
const emit = defineEmits([
  'update:current-page',
  'update:items-per-page',
  'prev-page',
  'next-page'
])

// Reactive screen size
const isMobile = ref(false)

const updateScreenSize = () => {
  isMobile.value = window.innerWidth < 640
}

onMounted(() => {
  updateScreenSize()
  window.addEventListener('resize', updateScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenSize)
})

// Computed
const startItem = computed(() => {
  return (props.currentPage - 1) * props.itemsPerPage + 1
})

const endItem = computed(() => {
  return Math.min(props.currentPage * props.itemsPerPage, props.totalItems)
})

const visiblePages = computed(() => {
  const pages = []
  const current = props.currentPage
  const total = props.totalPages

  // For mobile, show fewer pages
  const maxVisible = isMobile.value ? 3 : props.maxVisiblePages

  if (total <= 1) {
    return [1]
  }

  if (total <= maxVisible) {
    // Show all pages if total is less than max visible
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // Smart pagination logic
    if (current <= 3) {
      // Near the beginning
      for (let i = 1; i <= Math.min(maxVisible - 1, total); i++) {
        pages.push(i)
      }
      if (total > maxVisible - 1) {
        pages.push('...')
        pages.push(total)
      }
    } else if (current >= total - 2) {
      // Near the end
      pages.push(1)
      if (total > maxVisible) {
        pages.push('...')
      }
      for (let i = Math.max(total - maxVisible + 2, 2); i <= total; i++) {
        pages.push(i)
      }
    } else {
      // In the middle
      pages.push(1)
      pages.push('...')

      // Show current page and neighbors
      const start = Math.max(2, current - 1)
      const end = Math.min(total - 1, current + 1)

      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      pages.push('...')
      pages.push(total)
    }
  }

  return pages
})
</script>

<style scoped>
/* Custom pagination styles */
.pagination-button {
  transition: all 0.15s ease-in-out;
}
</style>
