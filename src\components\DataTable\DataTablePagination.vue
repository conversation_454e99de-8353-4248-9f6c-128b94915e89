<template>
  <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <!-- Items per page and info -->
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <label class="text-sm text-gray-700">Show:</label>
          <select :value="itemsPerPage" @change="$emit('update:items-per-page', parseInt($event.target.value))"
            class="border border-gray-300 rounded-md px-2 py-1 text-sm bg-white text-gray-900 focus:ring-2 focus:ring-primary focus:border-transparent">
            <option v-for="option in itemsPerPageOptions" :key="option" :value="option">
              {{ option }}
            </option>
          </select>
          <span class="text-sm text-gray-700">per page</span>
        </div>

        <!-- Results info -->
        <div class="text-sm text-gray-700">
          Showing {{ startItem }} to {{ endItem }} of {{ totalItems }} results
        </div>
      </div>

      <!-- Pagination controls -->
      <div class="flex items-center space-x-1">
        <!-- Previous button -->
        <button :disabled="currentPage === 1" @click="$emit('prev-page')" :class="[
          'relative inline-flex items-center px-2 py-2 text-sm font-medium rounded-l-md border',
          currentPage === 1
            ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
            : 'bg-white text-gray-500 border-gray-300 hover:bg-gray-50'
        ]">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
              clip-rule="evenodd" />
          </svg>
        </button>

        <!-- Page numbers -->
        <template v-for="page in visiblePages" :key="page">
          <button v-if="page !== '...'" :class="[
            'relative inline-flex items-center px-4 py-2 text-sm font-medium border',
            page === currentPage
              ? 'bg-primary text-white border-primary'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          ]" @click="$emit('update:current-page', page)">
            {{ page }}
          </button>
          <span v-else
            class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300">
            ...
          </span>
        </template>

        <!-- Next button -->
        <button :disabled="currentPage === totalPages" @click="$emit('next-page')" :class="[
          'relative inline-flex items-center px-2 py-2 text-sm font-medium rounded-r-md border',
          currentPage === totalPages
            ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
            : 'bg-white text-gray-500 border-gray-300 hover:bg-gray-50'
        ]">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
              clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile pagination (simplified) -->
    <div class="flex justify-between items-center sm:hidden mt-4">
      <button :disabled="currentPage === 1" @click="$emit('prev-page')" :class="[
        'relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md border',
        currentPage === 1
          ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
      ]">
        Previous
      </button>

      <span class="text-sm text-gray-700">
        Page {{ currentPage }} of {{ totalPages }}
      </span>

      <button :disabled="currentPage === totalPages" @click="$emit('next-page')" :class="[
        'relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md border',
        currentPage === totalPages
          ? 'bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed'
          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
      ]">
        Next
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  totalPages: {
    type: Number,
    required: true
  },
  totalItems: {
    type: Number,
    required: true
  },
  itemsPerPage: {
    type: Number,
    required: true
  },
  itemsPerPageOptions: {
    type: Array,
    default: () => [5, 10, 25, 50, 100]
  },
  maxVisiblePages: {
    type: Number,
    default: 7
  }
})

// Emits
const emit = defineEmits([
  'update:current-page',
  'update:items-per-page',
  'prev-page',
  'next-page'
])

// Computed
const startItem = computed(() => {
  return (props.currentPage - 1) * props.itemsPerPage + 1
})

const endItem = computed(() => {
  return Math.min(props.currentPage * props.itemsPerPage, props.totalItems)
})

const visiblePages = computed(() => {
  const pages = []
  const maxVisible = props.maxVisiblePages
  const current = props.currentPage
  const total = props.totalPages

  if (total <= maxVisible) {
    // Show all pages if total is less than max visible
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // Calculate start and end of visible range
    let start = Math.max(1, current - Math.floor(maxVisible / 2))
    let end = Math.min(total, start + maxVisible - 1)

    // Adjust start if we're near the end
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1)
    }

    // Add first page and ellipsis if needed
    if (start > 1) {
      pages.push(1)
      if (start > 2) {
        pages.push('...')
      }
    }

    // Add visible pages
    for (let i = start; i <= end; i++) {
      pages.push(i)
    }

    // Add ellipsis and last page if needed
    if (end < total) {
      if (end < total - 1) {
        pages.push('...')
      }
      pages.push(total)
    }
  }

  return pages
})
</script>

<style scoped>
/* Custom pagination styles */
.pagination-button {
  transition: all 0.15s ease-in-out;
}
</style>
