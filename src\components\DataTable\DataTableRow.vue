<template>
  <tr :class="[
    'hover:bg-gray-50 transition-colors duration-150',
    isSelected ? 'bg-blue-50' : '',
    config.clickableRows ? 'cursor-pointer' : ''
  ]" @click="handleRowClick">
    <!-- Selection Column -->
    <td v-if="config.selectable" class="relative w-12 px-6 sm:w-16 sm:px-8">
      <input type="checkbox" :checked="isSelected" @click.stop @change="$emit('toggle-selection', row, index)"
        class="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
    </td>

    <!-- Data Columns -->
    <td v-for="column in visibleColumns" :key="column.key" :class="[
      'px-6 py-4 whitespace-nowrap text-sm',
      column.cellClass || '',
      getCellAlignment(column)
    ]">
      <!-- Custom Slot Content -->
      <slot v-if="$slots[`cell-${column.key}`]" :name="`cell-${column.key}`" :row="row" :column="column"
        :value="getCellValue(row, column.key)" :index="index" />

      <!-- Built-in Cell Types -->
      <div v-else-if="column.type === 'avatar'" class="flex items-center">
        <div class="flex-shrink-0 h-10 w-10">
          <img :src="getCellValue(row, column.key) || '/default-avatar.png'"
            :alt="getCellValue(row, column.nameKey || 'name') || 'Avatar'" class="h-10 w-10 rounded-full object-cover"
            @error="handleImageError" />
        </div>
        <div v-if="column.showName" class="ml-4">
          <div class="text-sm font-medium text-gray-900">
            {{ getCellValue(row, column.nameKey || 'name') }}
          </div>
          <div v-if="column.emailKey" class="text-sm text-gray-500">
            {{ getCellValue(row, column.emailKey) }}
          </div>
        </div>
      </div>

      <div v-else-if="column.type === 'badge'" class="inline-flex">
        <span :class="[
          'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
          getBadgeClass(getCellValue(row, column.key), column.badgeColors)
        ]">
          {{ formatCellValue(getCellValue(row, column.key), column) }}
        </span>
      </div>

      <div v-else-if="column.type === 'boolean'" class="flex items-center">
        <div :class="[
          'flex-shrink-0 w-2 h-2 rounded-full',
          getCellValue(row, column.key) ? 'bg-green-400' : 'bg-red-400'
        ]"></div>
        <span class="ml-2 text-sm text-gray-900">
          {{ getCellValue(row, column.key) ? (column.trueText || 'Yes') : (column.falseText || 'No') }}
        </span>
      </div>

      <div v-else-if="column.type === 'date'" class="text-gray-900">
        {{ formatDate(getCellValue(row, column.key), column.dateFormat) }}
      </div>

      <div v-else-if="column.type === 'currency'" class="text-gray-900 font-medium">
        {{ formatCurrency(getCellValue(row, column.key), column.currency) }}
      </div>

      <div v-else-if="column.type === 'progress'" class="w-full">
        <div class="flex items-center">
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div :class="[
              'h-2 rounded-full transition-all duration-300',
              getProgressColor(getCellValue(row, column.key))
            ]" :style="{ width: `${Math.min(100, Math.max(0, getCellValue(row, column.key)))}%` }"></div>
          </div>
          <span class="ml-2 text-xs text-gray-600">
            {{ getCellValue(row, column.key) }}%
          </span>
        </div>
      </div>

      <div v-else-if="column.type === 'link'" class="text-primary hover:text-primary-hbr">
        <a :href="getCellValue(row, column.key)" :target="column.target || '_blank'" class="hover:underline"
          @click.stop>
          {{ column.linkText || getCellValue(row, column.key) }}
        </a>
      </div>

      <div v-else-if="column.type === 'actions'" :class="getCellAlignment(column)">
        <slot :name="`cell-${column.key}`" :row="row" :column="column" :value="getCellValue(row, column.key)"
          :index="index" :id="getRowId(row)">
          <!-- Default actions if no slot provided -->
          <div class="flex items-center justify-center space-x-2">
            <button v-if="column.actions?.includes('edit')" @click.stop="$emit('edit', row, getRowId(row))"
              class="text-blue-600 hover:text-blue-800 text-sm">
              Edit
            </button>
            <button v-if="column.actions?.includes('delete')" @click.stop="$emit('delete', row, getRowId(row))"
              class="text-red-600 hover:text-red-800 text-sm">
              Delete
            </button>
          </div>
        </slot>
      </div>

      <!-- Default Text Content -->
      <div v-else :class="getTextColor(column)">
        {{ formatCellValue(getCellValue(row, column.key), column) }}
      </div>
    </td>
  </tr>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  row: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  columns: {
    type: Array,
    required: true
  },
  config: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  idColumn: {
    type: String,
    default: 'id'
  }
})

// Emits
const emit = defineEmits(['toggle-selection', 'edit', 'delete'])

// Computed
const visibleColumns = computed(() => {
  return props.columns.filter(column => !column.hideOnTable)
})

// Methods
const getCellValue = (row, key) => {
  return key.split('.').reduce((obj, k) => obj?.[k], row)
}

const formatCellValue = (value, column) => {
  if (value === null || value === undefined) return column.emptyText || '-'

  if (column.formatter && typeof column.formatter === 'function') {
    return column.formatter(value, props.row)
  }

  return String(value)
}

const formatDate = (value, format = 'short') => {
  if (!value) return '-'

  const date = new Date(value)
  if (isNaN(date.getTime())) return value

  const options = {
    short: { year: 'numeric', month: 'short', day: 'numeric' },
    long: { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' },
    time: { hour: '2-digit', minute: '2-digit' }
  }

  return date.toLocaleDateString('en-US', options[format] || options.short)
}

const formatCurrency = (value, currency = 'USD') => {
  if (value === null || value === undefined) return '-'

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(value)
}

const getBadgeClass = (value, colors = {}) => {
  const defaultColors = {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-red-100 text-red-800',
    pending: 'bg-yellow-100 text-yellow-800',
    1: 'bg-green-100 text-green-800',      // Active (numeric)
    0: 'bg-red-100 text-red-800',         // Inactive (numeric)
    '1': 'bg-green-100 text-green-800',   // Active (string)
    '0': 'bg-red-100 text-red-800',       // Inactive (string)
    default: 'bg-gray-100 text-gray-800'
  }

  const colorMap = { ...defaultColors, ...colors }

  // Try exact value match first (for numeric values)
  if (colorMap[value] !== undefined) {
    return colorMap[value]
  }

  // Then try string/lowercase match
  const key = String(value).toLowerCase()
  return colorMap[key] || colorMap.default
}

const getProgressColor = (value) => {
  if (value >= 80) return 'bg-green-500'
  if (value >= 60) return 'bg-blue-500'
  if (value >= 40) return 'bg-yellow-500'
  return 'bg-red-500'
}

const getCellAlignment = (column) => {
  const alignments = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  }

  return alignments[column.align] || 'text-left'
}

const getTextColor = (column) => {
  return column.textColor || 'text-gray-900'
}

const handleRowClick = () => {
  if (props.config.clickableRows) {
    emit('row-click', props.row, props.index)
  }
}

const handleImageError = (event) => {
  event.target.src = '/default-avatar.png'
}

const getRowId = (row) => {
  return getCellValue(row, props.idColumn) || props.index
}
</script>

<style scoped>
/* Custom styles for row interactions */
.row-hover {
  transition: background-color 0.15s ease-in-out;
}
</style>
